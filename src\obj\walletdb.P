obj/walletdb.o: walletdb.cpp walletdb.h db.h main.h bignum.h util.h \
 uint256.h netbase.h serialize.h allocators.h version.h compat.h net.h \
 mruset.h protocol.h addrman.h sync.h key.h script.h keystore.h crypter.h \
 scrypt.h base58.h wallet.h ui_interface.h
walletdb.cpp walletdb.h db.h main.h bignum.h util.h :
 uint256.h netbase.h serialize.h allocators.h version.h compat.h net.h :
 mruset.h protocol.h addrman.h sync.h key.h script.h keystore.h crypter.h :
 scrypt.h base58.h wallet.h ui_interface.h :
