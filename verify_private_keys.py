#!/usr/bin/env python3
"""
Bellscoin Genesis Private Key Verification Script
=================================================

This script tests the most promising private key candidates discovered through
comprehensive analysis to see if any generate the target genesis public key.

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import binascii
from typing import Optional, Tuple
import base58

try:
    from ecdsa import SigningKey, SECP256k1
    from ecdsa.util import string_to_number, number_to_string
except ImportError:
    print("ERROR: ecdsa library not found. Install with: pip install ecdsa")
    exit(1)

# Target genesis public key (uncompressed format)
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def sha256_hash(data: str) -> str:
    """Calculate SHA256 hash of input string"""
    return hashlib.sha256(data.encode('utf-8')).hexdigest()

def private_key_to_public_key(private_key_hex: str, compressed: bool = False) -> str:
    """
    Convert private key to public key using secp256k1 elliptic curve

    Args:
        private_key_hex: Private key as hex string
        compressed: Whether to return compressed public key format

    Returns:
        Public key as hex string
    """
    try:
        # Convert hex to integer
        private_key_int = int(private_key_hex, 16)

        # Check if private key is in valid range
        if private_key_int <= 0 or private_key_int >= SECP256k1.order:
            return None

        # Create signing key from private key
        private_key_bytes = number_to_string(private_key_int, SECP256k1.order)
        signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)

        # Get public key
        verifying_key = signing_key.get_verifying_key()

        if compressed:
            # Compressed format: 02/03 + x coordinate
            x = verifying_key.pubkey.point.x()
            y = verifying_key.pubkey.point.y()
            prefix = "02" if y % 2 == 0 else "03"
            x_hex = format(x, '064x')
            return prefix + x_hex
        else:
            # Uncompressed format: 04 + x + y coordinates
            return "04" + verifying_key.to_string().hex()

    except Exception as e:
        print(f"Error generating public key: {e}")
        return None

def private_key_to_wif(private_key_hex: str, compressed: bool = False, testnet: bool = False) -> str:
    """
    Convert private key to Wallet Import Format (WIF)

    Args:
        private_key_hex: Private key as hex string
        compressed: Whether this is for compressed public key
        testnet: Whether this is for testnet

    Returns:
        WIF encoded private key
    """
    try:
        # Version byte (0x80 for mainnet, 0xEF for testnet)
        version = 0xEF if testnet else 0x80

        # Private key bytes
        private_key_bytes = bytes.fromhex(private_key_hex)

        # Add version byte
        extended_key = bytes([version]) + private_key_bytes

        # Add compression flag if needed
        if compressed:
            extended_key += b'\x01'

        # Double SHA256 for checksum
        checksum = hashlib.sha256(hashlib.sha256(extended_key).digest()).digest()[:4]

        # Final WIF
        wif_bytes = extended_key + checksum

        return base58.b58encode(wif_bytes).decode('ascii')

    except Exception as e:
        print(f"Error converting to WIF: {e}")
        return None

def test_private_key_candidate(name: str, private_key_hex: str) -> bool:
    """
    Test a private key candidate against the target genesis public key

    Args:
        name: Descriptive name for this candidate
        private_key_hex: Private key as hex string

    Returns:
        True if this key generates the target public key
    """
    print(f"\n{'='*60}")
    print(f"Testing: {name}")
    print(f"Private Key: {private_key_hex}")
    print(f"{'='*60}")

    # Test uncompressed public key
    uncompressed_pubkey = private_key_to_public_key(private_key_hex, compressed=False)
    if uncompressed_pubkey:
        print(f"Generated Uncompressed: {uncompressed_pubkey}")
        print(f"Target Public Key:      {TARGET_PUBKEY}")

        if uncompressed_pubkey.lower() == TARGET_PUBKEY.lower():
            print(f"\n🎉 MATCH FOUND! 🎉")
            print(f"🔑 TREASURE KEY DISCOVERED! 🔑")
            print(f"\nPrivate Key (hex): {private_key_hex}")

            # Convert to WIF formats
            wif_uncompressed = private_key_to_wif(private_key_hex, compressed=False)
            wif_compressed = private_key_to_wif(private_key_hex, compressed=True)

            if wif_uncompressed:
                print(f"WIF (uncompressed): {wif_uncompressed}")
            if wif_compressed:
                print(f"WIF (compressed): {wif_compressed}")

            return True
        else:
            print("❌ No match (uncompressed)")
    else:
        print("❌ Failed to generate uncompressed public key")

    # Test compressed public key
    compressed_pubkey = private_key_to_public_key(private_key_hex, compressed=True)
    if compressed_pubkey:
        print(f"Generated Compressed:   {compressed_pubkey}")
        # Note: Target is uncompressed, but let's check anyway
        print("❌ No match (compressed - target is uncompressed)")
    else:
        print("❌ Failed to generate compressed public key")

    return False

def main():
    """Main verification function"""
    print("🔍 Bellscoin Genesis Private Key Verification")
    print("=" * 50)
    print(f"Target Genesis Public Key:")
    print(f"{TARGET_PUBKEY}")
    print("\nTesting promising private key candidates...")

    # Define all candidates to test - CORRECTED VALUES FROM MAIN.CPP
    candidates = [
        # Direct hash candidates from analysis
        ("SHA256('Nintendo')", "0bee31ac676c1c2cfc04193f0e3859b88342603188bcadb417e31c3f090255b2"),
        ("SHA256('Nintondo')", sha256_hash("Nintondo")),
        ("SHA256('Nintondo2013')", "21feabaa270dcaff618be005b5af96d3770587df05c2455cf12f5f6b85ddf076"),

        # CORRECTED GENESIS VALUES FROM MAIN.CPP
        ("SHA256('Nintondo1383509530')", sha256_hash("Nintondo1383509530")),
        ("SHA256('Nintondo44481')", sha256_hash("Nintondo44481")),
        ("SHA256('138350953044481')", sha256_hash("138350953044481")),
        ("SHA256('1383509530')", sha256_hash("1383509530")),
        ("SHA256('44481')", sha256_hash("44481")),

        # XOR result candidates
        ("XOR Result (hex)", "000000000000000000000000000000000000000000000000000000002e49534d"),
        ("SHA256(XOR Result)", "9f6a969b56d017bf70d090fe967becaf3245acc954f05cf27d5ad7209b51aa7b"),

        # Additional string variations
        ("SHA256('Nintendo2013')", sha256_hash("Nintendo2013")),
        ("SHA256('Nintendo_2013')", sha256_hash("Nintendo_2013")),
        ("SHA256('Nintendo 2013')", sha256_hash("Nintendo 2013")),
        ("SHA256('nintendo')", sha256_hash("nintendo")),
        ("SHA256('NINTENDO')", sha256_hash("NINTENDO")),
        ("SHA256('Nintendo1383465600')", sha256_hash("Nintendo1383465600")),
        ("SHA256('Nintendo44481')", sha256_hash("Nintendo44481")),

        # Additional variations found in analysis
        ("SHA256('Bellscoin')", sha256_hash("Bellscoin")),
        ("SHA256('bellscoin')", sha256_hash("bellscoin")),
        ("SHA256('BELLSCOIN')", sha256_hash("BELLSCOIN")),
        ("SHA256('Bells')", sha256_hash("Bells")),
        ("SHA256('bells')", sha256_hash("bells")),

        # Message start bytes
        ("SHA256('c0c0c0c0')", sha256_hash("c0c0c0c0")),
        ("SHA256('Nintondoc0c0c0c0')", sha256_hash("Nintondoc0c0c0c0")),

        # Combined variations with CORRECT values
        ("SHA256('Bellscoin2013')", sha256_hash("Bellscoin2013")),
        ("SHA256('Bellscoin1383509530')", sha256_hash("Bellscoin1383509530")),
        ("SHA256('Bellscoin44481')", sha256_hash("Bellscoin44481")),

        # Dogecoin connection (launched Dec 2013)
        ("SHA256('NintondoDogecoin')", sha256_hash("NintondoDogecoin")),
        ("SHA256('DogecoinNintondo')", sha256_hash("DogecoinNintondo")),
    ]

    # Test each candidate
    matches_found = 0
    total_candidates = len(candidates)

    for i, (name, private_key_hex) in enumerate(candidates, 1):
        print(f"\n[{i}/{total_candidates}] ", end="")

        if test_private_key_candidate(name, private_key_hex):
            matches_found += 1
            print(f"\n🎯 CRITICAL: TREASURE KEY FOUND!")
            print(f"🎯 This is the private key that generates the genesis public key!")
            break  # Stop on first match

    # Final summary
    print(f"\n{'='*60}")
    print(f"VERIFICATION COMPLETE")
    print(f"{'='*60}")
    print(f"Total candidates tested: {total_candidates}")
    print(f"Matches found: {matches_found}")

    if matches_found == 0:
        print(f"\n❌ No matches found among the tested candidates.")
        print(f"💡 The treasure key may require additional analysis or different approaches.")
        print(f"💡 Consider testing more variations or checking the analysis methodology.")
    else:
        print(f"\n🎉 SUCCESS! The treasure hunt is complete!")
        print(f"🔑 The hidden private key has been discovered!")

if __name__ == "__main__":
    main()