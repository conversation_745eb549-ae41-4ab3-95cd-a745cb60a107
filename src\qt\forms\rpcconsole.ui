<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RPCConsole</class>
 <widget class="QDialog" name="RPCConsole">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>740</width>
    <height>450</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>DogeCoin - Debug window</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tab_info">
      <attribute name="title">
       <string>&amp;Information</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout" columnstretch="0,1">
       <property name="horizontalSpacing">
        <number>12</number>
       </property>
       <item row="0" column="0">
        <widget class="QLabel" name="label_9">
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
         <property name="text">
          <string>Bitcoin Core</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="label_5">
         <property name="text">
          <string>Client name</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QLabel" name="clientName">
         <property name="cursor">
          <cursorShape>IBeamCursor</cursorShape>
         </property>
         <property name="text">
          <string>N/A</string>
         </property>
         <property name="textFormat">
          <enum>Qt::PlainText</enum>
         </property>
         <property name="textInteractionFlags">
          <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByKeyboard|Qt::TextSelectableByMouse</set>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="label_6">
         <property name="text">
          <string>Client version</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QLabel" name="clientVersion">
         <property name="cursor">
          <cursorShape>IBeamCursor</cursorShape>
         </property>
         <property name="text">
          <string>N/A</string>
         </property>
         <property name="textFormat">
          <enum>Qt::PlainText</enum>
         </property>
         <property name="textInteractionFlags">
          <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByKeyboard|Qt::TextSelectableByMouse</set>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="label_14">
         <property name="text">
          <string>Using OpenSSL version</string>
         </property>
         <property name="indent">
          <number>10</number>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QLabel" name="openSSLVersion">
         <property name="cursor">
          <cursorShape>IBeamCursor</cursorShape>
         </property>
         <property name="text">
          <string>N/A</string>
         </property>
         <property name="textFormat">
          <enum>Qt::PlainText</enum>
         </property>
         <property name="textInteractionFlags">
          <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByKeyboard|Qt::TextSelectableByMouse</set>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="label_12">
         <property name="text">
          <string>Build date</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="QLabel" name="buildDate">
         <property name="cursor">
          <cursorShape>IBeamCursor</cursorShape>
         </property>
         <property name="text">
          <string>N/A</string>
         </property>
         <property name="textFormat">
          <enum>Qt::PlainText</enum>
         </property>
         <property name="textInteractionFlags">
          <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByKeyboard|Qt::TextSelectableByMouse</set>
         </property>
        </widget>
       </item>
       <item row="5" column="0">
        <widget class="QLabel" name="label_13">
         <property name="text">
          <string>Startup time</string>
         </property>
        </widget>
       </item>
       <item row="5" column="1">
        <widget class="QLabel" name="startupTime">
         <property name="cursor">
          <cursorShape>IBeamCursor</cursorShape>
         </property>
         <property name="text">
          <string>N/A</string>
         </property>
         <property name="textFormat">
          <enum>Qt::PlainText</enum>
         </property>
         <property name="textInteractionFlags">
          <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByKeyboard|Qt::TextSelectableByMouse</set>
         </property>
        </widget>
       </item>
       <item row="6" column="0">
        <widget class="QLabel" name="label_11">
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
         <property name="text">
          <string>Network</string>
         </property>
        </widget>
       </item>
       <item row="7" column="0">
        <widget class="QLabel" name="label_7">
         <property name="text">
          <string>Number of connections</string>
         </property>
        </widget>
       </item>
       <item row="7" column="1">
        <widget class="QLabel" name="numberOfConnections">
         <property name="cursor">
          <cursorShape>IBeamCursor</cursorShape>
         </property>
         <property name="text">
          <string>N/A</string>
         </property>
         <property name="textFormat">
          <enum>Qt::PlainText</enum>
         </property>
         <property name="textInteractionFlags">
          <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByKeyboard|Qt::TextSelectableByMouse</set>
         </property>
        </widget>
       </item>
       <item row="8" column="0">
        <widget class="QLabel" name="label_8">
         <property name="text">
          <string>On testnet</string>
         </property>
        </widget>
       </item>
       <item row="8" column="1">
        <widget class="QCheckBox" name="isTestNet">
         <property name="enabled">
          <bool>false</bool>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item row="9" column="0">
        <widget class="QLabel" name="label_10">
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
         <property name="text">
          <string>Block chain</string>
         </property>
        </widget>
       </item>
       <item row="10" column="0">
        <widget class="QLabel" name="label_3">
         <property name="text">
          <string>Current number of blocks</string>
         </property>
        </widget>
       </item>
       <item row="10" column="1">
        <widget class="QLabel" name="numberOfBlocks">
         <property name="cursor">
          <cursorShape>IBeamCursor</cursorShape>
         </property>
         <property name="text">
          <string>N/A</string>
         </property>
         <property name="textFormat">
          <enum>Qt::PlainText</enum>
         </property>
         <property name="textInteractionFlags">
          <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByKeyboard|Qt::TextSelectableByMouse</set>
         </property>
        </widget>
       </item>
       <item row="11" column="0">
        <widget class="QLabel" name="label_4">
         <property name="text">
          <string>Estimated total blocks</string>
         </property>
        </widget>
       </item>
       <item row="11" column="1">
        <widget class="QLabel" name="totalBlocks">
         <property name="cursor">
          <cursorShape>IBeamCursor</cursorShape>
         </property>
         <property name="text">
          <string>N/A</string>
         </property>
         <property name="textFormat">
          <enum>Qt::PlainText</enum>
         </property>
         <property name="textInteractionFlags">
          <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByKeyboard|Qt::TextSelectableByMouse</set>
         </property>
        </widget>
       </item>
       <item row="12" column="0">
        <widget class="QLabel" name="label_2">
         <property name="text">
          <string>Last block time</string>
         </property>
        </widget>
       </item>
       <item row="12" column="1">
        <widget class="QLabel" name="lastBlockTime">
         <property name="cursor">
          <cursorShape>IBeamCursor</cursorShape>
         </property>
         <property name="text">
          <string>N/A</string>
         </property>
         <property name="textFormat">
          <enum>Qt::PlainText</enum>
         </property>
         <property name="textInteractionFlags">
          <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByKeyboard|Qt::TextSelectableByMouse</set>
         </property>
        </widget>
       </item>
       <item row="13" column="0">
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="14" column="0">
        <widget class="QLabel" name="labelDebugLogfile">
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
         <property name="text">
          <string>Debug logfile</string>
         </property>
        </widget>
       </item>
       <item row="15" column="0">
        <widget class="QPushButton" name="openDebugLogfileButton">
         <property name="toolTip">
          <string>Open the DogeCoin debug logfile from the current data directory. This can take a few seconds for large logfiles.</string>
         </property>
         <property name="text">
          <string>&amp;Open</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="16" column="0">
        <widget class="QLabel" name="labelCLOptions">
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
         <property name="text">
          <string>Command-line options</string>
         </property>
        </widget>
       </item>
       <item row="17" column="0">
        <widget class="QPushButton" name="showCLOptionsButton">
         <property name="toolTip">
          <string>Show the DogeCoin-Qt help message to get a list with possible DogeCoin command-line options.</string>
         </property>
         <property name="text">
          <string>&amp;Show</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="18" column="0">
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_console">
      <attribute name="title">
       <string>&amp;Console</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <property name="spacing">
        <number>3</number>
       </property>
       <item>
        <widget class="QTextEdit" name="messagesWidget">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>100</height>
          </size>
         </property>
         <property name="readOnly">
          <bool>true</bool>
         </property>
         <property name="tabKeyNavigation" stdset="0">
          <bool>false</bool>
         </property>
         <property name="columnCount" stdset="0">
          <number>2</number>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <property name="spacing">
          <number>3</number>
         </property>
         <item>
          <widget class="QLabel" name="label">
           <property name="text">
            <string notr="true">&gt;</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEdit"/>
         </item>
         <item>
          <widget class="QPushButton" name="clearButton">
           <property name="maximumSize">
            <size>
             <width>24</width>
             <height>24</height>
            </size>
           </property>
           <property name="toolTip">
            <string>Clear console</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset resource="../bitcoin.qrc">
             <normaloff>:/icons/remove</normaloff>:/icons/remove</iconset>
           </property>
           <property name="shortcut">
            <string notr="true">Ctrl+L</string>
           </property>
           <property name="autoDefault">
            <bool>false</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../bitcoin.qrc"/>
 </resources>
 <connections/>
</ui>
