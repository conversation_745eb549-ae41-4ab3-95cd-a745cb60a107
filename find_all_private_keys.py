#!/usr/bin/env python3
"""
Find All Private Keys in Codebase
=================================

This script searches the entire Bellscoin codebase for:
1. WIF format private keys (starting with 6, T, L, K, etc.)
2. 64-character hex strings that could be private keys
3. Test vectors and hardcoded keys
4. Any other potential private key formats
"""

import os
import re
import hashlib
from typing import List, Dict, Set
from ecdsa import SigningKey, SECP256k1
from ecdsa.util import string_to_number, number_to_string

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def test_private_key(private_key_hex: str) -> bool:
    """Test if a private key generates the target public key"""
    try:
        if len(private_key_hex) != 64:
            private_key_hex = private_key_hex.zfill(64)
        
        private_key_int = int(private_key_hex, 16)
        if private_key_int == 0 or private_key_int >= SECP256k1.order:
            return False
        
        private_key_bytes = number_to_string(private_key_int, SECP256k1.order)
        signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        verifying_key = signing_key.get_verifying_key()
        
        uncompressed_pubkey = "04" + verifying_key.to_string().hex()
        return uncompressed_pubkey.lower() == TARGET_PUBKEY.lower()
    except:
        return False

def find_wif_keys(content: str) -> List[str]:
    """Find WIF format private keys"""
    wif_keys = []
    
    # WIF patterns (Base58 encoded private keys)
    # Mainnet: starts with 5, K, L (uncompressed/compressed)
    # Testnet: starts with 9, c (uncompressed/compressed)
    # Litecoin: starts with 6, T (uncompressed/compressed)
    wif_patterns = [
        r'\b[5KL][1-9A-HJ-NP-Za-km-z]{50,51}\b',  # Bitcoin mainnet
        r'\b[9c][1-9A-HJ-NP-Za-km-z]{50,51}\b',   # Bitcoin testnet
        r'\b[6T][1-9A-HJ-NP-Za-km-z]{50,51}\b',   # Litecoin/Bellscoin
        r'\b[1-9A-HJ-NP-Za-km-z]{51,52}\b',       # Generic Base58 (51-52 chars)
    ]
    
    for pattern in wif_patterns:
        matches = re.findall(pattern, content)
        wif_keys.extend(matches)
    
    return wif_keys

def find_hex_keys(content: str) -> List[str]:
    """Find potential hex private keys"""
    hex_keys = []
    
    # 64-character hex strings (256-bit keys)
    hex_pattern = r'\b[0-9a-fA-F]{64}\b'
    matches = re.findall(hex_pattern, content)
    hex_keys.extend(matches)
    
    # Quoted hex strings
    quoted_patterns = [
        r'["\']([0-9a-fA-F]{64})["\']',
        r'["\']([0-9a-fA-F]{32})["\']',  # 32-char hex (128-bit)
    ]
    
    for pattern in quoted_patterns:
        matches = re.findall(pattern, content)
        hex_keys.extend(matches)
    
    return hex_keys

def find_test_vectors(content: str) -> List[str]:
    """Find test vectors and hardcoded keys"""
    test_keys = []
    
    # Look for variable assignments that might contain keys
    patterns = [
        r'static\s+const\s+string\s+\w*[Ss]ecret\w*\s*\(\s*["\']([^"\']+)["\']',
        r'static\s+const\s+string\s+\w*[Kk]ey\w*\s*\(\s*["\']([^"\']+)["\']',
        r'string\s+\w*[Ss]ecret\w*\s*=\s*["\']([^"\']+)["\']',
        r'string\s+\w*[Kk]ey\w*\s*=\s*["\']([^"\']+)["\']',
        r'const\s+char\s+\w*[Kk]ey\w*\[\]\s*=\s*["\']([^"\']+)["\']',
        r'const\s+char\s+\w*[Ss]ecret\w*\[\]\s*=\s*["\']([^"\']+)["\']',
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        test_keys.extend(matches)
    
    return test_keys

def scan_file_for_keys(filepath: str) -> Dict[str, List[str]]:
    """Scan a single file for all types of keys"""
    results = {
        'wif_keys': [],
        'hex_keys': [],
        'test_vectors': [],
        'file_path': filepath
    }
    
    try:
        with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
            results['wif_keys'] = find_wif_keys(content)
            results['hex_keys'] = find_hex_keys(content)
            results['test_vectors'] = find_test_vectors(content)
            
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
    
    return results

def scan_codebase() -> Dict[str, Dict[str, List[str]]]:
    """Scan the entire codebase for private keys"""
    all_results = {}
    
    # File extensions to scan
    extensions = ['.cpp', '.h', '.c', '.py', '.sh', '.txt', '.md', '.json', '.conf']
    
    for root, dirs, files in os.walk('.'):
        # Skip certain directories
        if any(skip in root for skip in ['.git', '__pycache__', 'obj']):
            continue
            
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                filepath = os.path.join(root, file)
                results = scan_file_for_keys(filepath)
                
                # Only store results if we found something
                if (results['wif_keys'] or results['hex_keys'] or results['test_vectors']):
                    all_results[filepath] = results
    
    return all_results

def analyze_found_keys(all_results: Dict[str, Dict[str, List[str]]]):
    """Analyze all found keys and test them"""
    print("🔍 COMPREHENSIVE PRIVATE KEY ANALYSIS")
    print("=" * 60)
    
    total_wif = 0
    total_hex = 0
    total_test = 0
    
    for filepath, results in all_results.items():
        print(f"\n📁 File: {filepath}")
        
        # Test WIF keys
        if results['wif_keys']:
            print(f"  🔑 WIF Keys found: {len(results['wif_keys'])}")
            total_wif += len(results['wif_keys'])
            for i, wif_key in enumerate(results['wif_keys'], 1):
                print(f"    [{i}] {wif_key}")
                # TODO: Convert WIF to hex and test
                
        # Test hex keys
        if results['hex_keys']:
            print(f"  🔢 Hex Keys found: {len(results['hex_keys'])}")
            total_hex += len(results['hex_keys'])
            for i, hex_key in enumerate(results['hex_keys'], 1):
                print(f"    [{i}] {hex_key[:32]}...")
                if test_private_key(hex_key):
                    print(f"    🎯 TREASURE FOUND! Hex key: {hex_key}")
                    return hex_key
                
        # Test test vectors
        if results['test_vectors']:
            print(f"  🧪 Test Vectors found: {len(results['test_vectors'])}")
            total_test += len(results['test_vectors'])
            for i, test_key in enumerate(results['test_vectors'], 1):
                print(f"    [{i}] {test_key}")
                
                # Test if it's a hex key
                if len(test_key) == 64 and all(c in '0123456789abcdefABCDEF' for c in test_key):
                    if test_private_key(test_key):
                        print(f"    🎯 TREASURE FOUND! Test vector: {test_key}")
                        return test_key
                
                # Test hash of test vector
                hash_result = hashlib.sha256(test_key.encode()).hexdigest()
                if test_private_key(hash_result):
                    print(f"    🎯 TREASURE FOUND! Hash of test vector: {test_key} -> {hash_result}")
                    return hash_result
    
    print(f"\n📊 SUMMARY:")
    print(f"  Total WIF keys found: {total_wif}")
    print(f"  Total Hex keys found: {total_hex}")
    print(f"  Total Test vectors found: {total_test}")
    print(f"  Total files with keys: {len(all_results)}")
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ COMPREHENSIVE PRIVATE KEY SEARCH")
    print("Searching the entire Bellscoin codebase for private keys...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Scan the codebase
    print("\n🔍 Scanning codebase for all types of private keys...")
    all_results = scan_codebase()
    
    if not all_results:
        print("❌ No private keys found in the codebase!")
        return
    
    # Analyze found keys
    result = analyze_found_keys(all_results)
    
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
    else:
        print("\n💔 No treasure found among the discovered keys")
        print("🔍 All found keys have been tested but none match the target...")

if __name__ == "__main__":
    main()
