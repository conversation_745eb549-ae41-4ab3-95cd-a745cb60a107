# The public key from the genesis block
pubkey = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

# Check if this is a standard Bitcoin public key
import binascii

# First byte should be 0x04 for uncompressed keys
first_byte = pubkey[:2]
print(f"First byte of public key: {first_byte}")

if first_byte == "04":
    print("This appears to be an uncompressed public key")
    x_coord = pubkey[2:66]
    y_coord = pubkey[66:]
    print(f"X coordinate: {x_coord}")
    print(f"Y coordinate: {y_coord}")