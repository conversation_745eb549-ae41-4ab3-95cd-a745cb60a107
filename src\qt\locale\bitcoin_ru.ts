<?xml version="1.0" ?><!DOCTYPE TS><TS language="ru" version="2.0">
<defaultcodec>UTF-8</defaultcodec>
<context>
    <name>AboutDialog</name>
    <message>
        <location filename="../forms/aboutdialog.ui" line="14"/>
        <source>About Bitcoin</source>
        <translation>О Bitcoin</translation>
    </message>
    <message>
        <location filename="../forms/aboutdialog.ui" line="53"/>
        <source>&lt;b&gt;Bitcoin&lt;/b&gt; version</source>
        <translation>&lt;b&gt;Bitcoin&lt;/b&gt; версия</translation>
    </message>
    <message>
        <location filename="../forms/aboutdialog.ui" line="97"/>
        <source>Copyright © 2009-2012 Bitcoin Developers

This is experimental software.

Distributed under the MIT/X11 software license, see the accompanying file license.txt or http://www.opensource.org/licenses/mit-license.php.

This product includes software developed by the OpenSSL Project for use in the OpenSSL Toolkit (http://www.openssl.org/) and cryptographic software written by <PERSON> (<EMAIL>) and UPnP software written by <PERSON>.</source>
        <translation>Все права защищены © 2009-2012 Разработчики Bitcoin

Это экспериментальная программа.

Распространяется на правах лицензии MIT/X11, см. файл license.txt или http://www.opensource.org/licenses/mit-license.php.

Этот продукт включает ПО, разработанное OpenSSL Project для использования в OpenSSL Toolkit (http://www.openssl.org/) и криптографическое ПО, написанное Eric Young (<EMAIL>) и ПО для работы с UPnP, написанное Thomas Bernard.</translation>
    </message>
</context>
<context>
    <name>AddressBookPage</name>
    <message>
        <location filename="../forms/addressbookpage.ui" line="14"/>
        <source>Address Book</source>
        <translation>Адресная книга</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="20"/>
        <source>These are your Bitcoin addresses for receiving payments.  You may want to give a different one to each sender so you can keep track of who is paying you.</source>
        <translation>Здесь перечислены Ваши адреса для получения платежей.  Вы можете использовать их для того, чтобы давать разным людям разные адреса и, таким образом, иметь возможность отслеживать, кто и сколько Вам платил, а также поддерживать бо́льшую анонимность.</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="36"/>
        <source>Double-click to edit address or label</source>
        <translation>Для того, чтобы изменить адрес или метку давжды кликните по изменяемому объекту</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="63"/>
        <source>Create a new address</source>
        <translation>Создать новый адрес</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="77"/>
        <source>Copy the currently selected address to the system clipboard</source>
        <translation>Копировать текущий выделенный адрес в буфер обмена</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="66"/>
        <source>&amp;New Address</source>
        <translation>&amp;Новый адрес</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="80"/>
        <source>&amp;Copy Address</source>
        <translation>&amp;Копировать адрес</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="91"/>
        <source>Show &amp;QR Code</source>
        <translation>Показать &amp;QR код</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="102"/>
        <source>Sign a message to prove you own this address</source>
        <translation>Подпишите сообщение для доказательства </translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="105"/>
        <source>&amp;Sign Message</source>
        <translation>&amp;Подписать сообщение</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="116"/>
        <source>Delete the currently selected address from the list. Only sending addresses can be deleted.</source>
        <translation>Удалить выделенный адрес из списка (могут быть удалены только записи из адресной книги).</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="119"/>
        <source>&amp;Delete</source>
        <translation>&amp;Удалить</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="63"/>
        <source>Copy &amp;Label</source>
        <translation>Копировать &amp;метку</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="65"/>
        <source>&amp;Edit</source>
        <translation>&amp;Правка</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="292"/>
        <source>Export Address Book Data</source>
        <translation>Экспортировать адресную книгу</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="293"/>
        <source>Comma separated file (*.csv)</source>
        <translation>Текст, разделённый запятыми (*.csv)</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="306"/>
        <source>Error exporting</source>
        <translation>Ошибка экспорта</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="306"/>
        <source>Could not write to file %1.</source>
        <translation>Невозможно записать в файл %1.</translation>
    </message>
</context>
<context>
    <name>AddressTableModel</name>
    <message>
        <location filename="../addresstablemodel.cpp" line="142"/>
        <source>Label</source>
        <translation>Метка</translation>
    </message>
    <message>
        <location filename="../addresstablemodel.cpp" line="142"/>
        <source>Address</source>
        <translation>Адрес</translation>
    </message>
    <message>
        <location filename="../addresstablemodel.cpp" line="178"/>
        <source>(no label)</source>
        <translation>[нет метки]</translation>
    </message>
</context>
<context>
    <name>AskPassphraseDialog</name>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="26"/>
        <source>Passphrase Dialog</source>
        <translation>Диалог ввода пароля</translation>
    </message>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="47"/>
        <source>Enter passphrase</source>
        <translation>Введите пароль</translation>
    </message>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="61"/>
        <source>New passphrase</source>
        <translation>Новый пароль</translation>
    </message>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="75"/>
        <source>Repeat new passphrase</source>
        <translation>Повторите новый пароль</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="33"/>
        <source>Enter the new passphrase to the wallet.&lt;br/&gt;Please use a passphrase of &lt;b&gt;10 or more random characters&lt;/b&gt;, or &lt;b&gt;eight or more words&lt;/b&gt;.</source>
        <translation>Введите новый пароль для бумажника. &lt;br/&gt; Пожалуйста, используйте фразы из &lt;b&gt;10 или более случайных символов,&lt;/b&gt; или &lt;b&gt;восьми и более слов.&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="34"/>
        <source>Encrypt wallet</source>
        <translation>Зашифровать бумажник</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="37"/>
        <source>This operation needs your wallet passphrase to unlock the wallet.</source>
        <translation>Для выполнения операции требуется пароль вашего бумажника.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="42"/>
        <source>Unlock wallet</source>
        <translation>Разблокировать бумажник</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="45"/>
        <source>This operation needs your wallet passphrase to decrypt the wallet.</source>
        <translation>Для выполнения операции требуется пароль вашего бумажника.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="50"/>
        <source>Decrypt wallet</source>
        <translation>Расшифровать бумажник</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="53"/>
        <source>Change passphrase</source>
        <translation>Сменить пароль</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="54"/>
        <source>Enter the old and new passphrase to the wallet.</source>
        <translation>Введите старый и новый пароль для бумажника.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="100"/>
        <source>Confirm wallet encryption</source>
        <translation>Подтвердите шифрование бумажника</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="101"/>
        <source>WARNING: If you encrypt your wallet and lose your passphrase, you will &lt;b&gt;LOSE ALL OF YOUR BITCOINS&lt;/b&gt;!
Are you sure you wish to encrypt your wallet?</source>
        <translation>ВНИМАНИЕ: Если вы зашифруете бумажник и потеряете свой ​​пароль, вы &lt;b&gt;ПОТЕРЯЕТЕ ВСЕ ВАШИ БИТКОИНЫ!&lt;/b&gt;
Вы действительно хотите зашифровать ваш бумажник?</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="110"/>
        <location filename="../askpassphrasedialog.cpp" line="159"/>
        <source>Wallet encrypted</source>
        <translation>Бумажник зашифрован</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="111"/>
        <source>Bitcoin will close now to finish the encryption process. Remember that encrypting your wallet cannot fully protect your bitcoins from being stolen by malware infecting your computer.</source>
        <translation>Сейчас программа закроется для завершения процесса шифрования. Помните, что шифрование вашего бумажника не может полностью защитить ваши биткоины от кражи с помощью инфицирования вашего компьютера вредоносным ПО.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="207"/>
        <location filename="../askpassphrasedialog.cpp" line="231"/>
        <source>Warning: The Caps Lock key is on.</source>
        <translation>Внимание: Caps Lock включен.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="116"/>
        <location filename="../askpassphrasedialog.cpp" line="123"/>
        <location filename="../askpassphrasedialog.cpp" line="165"/>
        <location filename="../askpassphrasedialog.cpp" line="171"/>
        <source>Wallet encryption failed</source>
        <translation>Не удалось зашифровать бумажник</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="117"/>
        <source>Wallet encryption failed due to an internal error. Your wallet was not encrypted.</source>
        <translation>Шифрование бумажника не удалось из-за внутренней ошибки. Ваш бумажник не был зашифрован.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="124"/>
        <location filename="../askpassphrasedialog.cpp" line="172"/>
        <source>The supplied passphrases do not match.</source>
        <translation>Введённые пароли не совпадают.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="135"/>
        <source>Wallet unlock failed</source>
        <translation>Разблокировка бумажника не удалась</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="136"/>
        <location filename="../askpassphrasedialog.cpp" line="147"/>
        <location filename="../askpassphrasedialog.cpp" line="166"/>
        <source>The passphrase entered for the wallet decryption was incorrect.</source>
        <translation>Указанный пароль не подходит.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="146"/>
        <source>Wallet decryption failed</source>
        <translation>Расшифрование бумажника не удалось</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="160"/>
        <source>Wallet passphrase was succesfully changed.</source>
        <translation>Пароль бумажника успешно изменён.</translation>
    </message>
</context>
<context>
    <name>BitcoinGUI</name>
    <message>
        <location filename="../bitcoingui.cpp" line="73"/>
        <source>Bitcoin Wallet</source>
        <translation>Bitcoin-бумажник</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="215"/>
        <source>Sign &amp;message...</source>
        <translation>&amp;Подписать сообщение</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="248"/>
        <source>Show/Hide &amp;Bitcoin</source>
        <translation>Показать/Скрыть &amp;Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="515"/>
        <source>Synchronizing with network...</source>
        <translation>Синхронизация с сетью...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="185"/>
        <source>&amp;Overview</source>
        <translation>О&amp;бзор</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="186"/>
        <source>Show general overview of wallet</source>
        <translation>Показать общий обзор действий с бумажником</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="191"/>
        <source>&amp;Transactions</source>
        <translation>&amp;Транзакции</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="192"/>
        <source>Browse transaction history</source>
        <translation>Показать историю транзакций</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="197"/>
        <source>&amp;Address Book</source>
        <translation>&amp;Адресная книга</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="198"/>
        <source>Edit the list of stored addresses and labels</source>
        <translation>Изменить список сохранённых адресов и меток к ним</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="203"/>
        <source>&amp;Receive coins</source>
        <translation>&amp;Получение монет</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="204"/>
        <source>Show the list of addresses for receiving payments</source>
        <translation>Показать список адресов для получения платежей</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="209"/>
        <source>&amp;Send coins</source>
        <translation>Отп&amp;равка монет</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="216"/>
        <source>Prove you control an address</source>
        <translation>Доказать, что вы владеете адресом</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="235"/>
        <source>E&amp;xit</source>
        <translation>В&amp;ыход</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="236"/>
        <source>Quit application</source>
        <translation>Закрыть приложение</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="239"/>
        <source>&amp;About %1</source>
        <translation>&amp;О %1</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="240"/>
        <source>Show information about Bitcoin</source>
        <translation>Показать информацию о Bitcoin&apos;е</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="242"/>
        <source>About &amp;Qt</source>
        <translation>О &amp;Qt</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="243"/>
        <source>Show information about Qt</source>
        <translation>Показать информацию о Qt</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="245"/>
        <source>&amp;Options...</source>
        <translation>Оп&amp;ции...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="252"/>
        <source>&amp;Encrypt Wallet...</source>
        <translation>&amp;Зашифровать бумажник</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="255"/>
        <source>&amp;Backup Wallet...</source>
        <translation>&amp;Сделать резервную копию бумажника</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="257"/>
        <source>&amp;Change Passphrase...</source>
        <translation>&amp;Изменить пароль</translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="517"/>
        <source>~%n block(s) remaining</source>
        <translation><numerusform>остался ~%n блок</numerusform><numerusform>осталось ~%n блоков</numerusform><numerusform>осталось ~%n блоков</numerusform></translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="528"/>
        <source>Downloaded %1 of %2 blocks of transaction history (%3% done).</source>
        <translation>Загружено %1 из %2 блоков истории операций (%3% завершено).</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="250"/>
        <source>&amp;Export...</source>
        <translation>&amp;Экспорт...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="210"/>
        <source>Send coins to a Bitcoin address</source>
        <translation>Отправить монеты на указанный адрес Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="246"/>
        <source>Modify configuration options for Bitcoin</source>
        <translation>Изменить параметры конфигурации Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="249"/>
        <source>Show or hide the Bitcoin window</source>
        <translation>Показать или скрыть окно Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="251"/>
        <source>Export the data in the current tab to a file</source>
        <translation>Экспортировать данные из вкладки в файл</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="253"/>
        <source>Encrypt or decrypt wallet</source>
        <translation>Зашифровать или расшифровать бумажник</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="256"/>
        <source>Backup wallet to another location</source>
        <translation>Сделать резервную копию бумажника в другом месте</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="258"/>
        <source>Change the passphrase used for wallet encryption</source>
        <translation>Изменить пароль шифрования бумажника</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="259"/>
        <source>&amp;Debug window</source>
        <translation>&amp;Окно отладки</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="260"/>
        <source>Open debugging and diagnostic console</source>
        <translation>Открыть консоль отладки и диагностики</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="261"/>
        <source>&amp;Verify message...</source>
        <translation>&amp;Проверить сообщение...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="262"/>
        <source>Verify a message signature</source>
        <translation>Проверить подпись сообщения</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="286"/>
        <source>&amp;File</source>
        <translation>&amp;Файл</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="296"/>
        <source>&amp;Settings</source>
        <translation>&amp;Настройки</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="302"/>
        <source>&amp;Help</source>
        <translation>&amp;Помощь</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="311"/>
        <source>Tabs toolbar</source>
        <translation>Панель вкладок</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="322"/>
        <source>Actions toolbar</source>
        <translation>Панель действий</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="334"/>
        <location filename="../bitcoingui.cpp" line="343"/>
        <source>[testnet]</source>
        <translation>[тестовая сеть]</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="343"/>
        <location filename="../bitcoingui.cpp" line="399"/>
        <source>Bitcoin client</source>
        <translation>Bitcoin клиент</translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="492"/>
        <source>%n active connection(s) to Bitcoin network</source>
        <translation><numerusform>%n активное соединение с сетью</numerusform><numerusform>%n активных соединений с сетью</numerusform><numerusform>%n активных соединений с сетью</numerusform></translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="540"/>
        <source>Downloaded %1 blocks of transaction history.</source>
        <translation>Загружено %1 блоков истории транзакций.</translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="555"/>
        <source>%n second(s) ago</source>
        <translation><numerusform>%n секунду назад</numerusform><numerusform>%n секунды назад</numerusform><numerusform>%n секунд назад</numerusform></translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="559"/>
        <source>%n minute(s) ago</source>
        <translation><numerusform>%n минуту назад</numerusform><numerusform>%n минуты назад</numerusform><numerusform>%n минут назад</numerusform></translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="563"/>
        <source>%n hour(s) ago</source>
        <translation><numerusform>%n час назад</numerusform><numerusform>%n часа назад</numerusform><numerusform>%n часов назад</numerusform></translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="567"/>
        <source>%n day(s) ago</source>
        <translation><numerusform>%n день назад</numerusform><numerusform>%n дня назад</numerusform><numerusform>%n дней назад</numerusform></translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="573"/>
        <source>Up to date</source>
        <translation>Синхронизированно</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="580"/>
        <source>Catching up...</source>
        <translation>Синхронизируется...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="590"/>
        <source>Last received block was generated %1.</source>
        <translation>Последний полученный блок был сгенерирован %1.</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="649"/>
        <source>This transaction is over the size limit.  You can still send it for a fee of %1, which goes to the nodes that process your transaction and helps to support the network.  Do you want to pay the fee?</source>
        <translation>Данная транзакция превышает предельно допустимый размер.  Но Вы можете всё равно совершить её, добавив комиссию в %1, которая отправится тем узлам, которые обработают Вашу транзакцию, и поможет поддержать сеть.  Вы хотите добавить комиссию?</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="654"/>
        <source>Confirm transaction fee</source>
        <translation>Подтвердите комиссию</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="681"/>
        <source>Sent transaction</source>
        <translation>Исходящая транзакция</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="682"/>
        <source>Incoming transaction</source>
        <translation>Входящая транзакция</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="683"/>
        <source>Date: %1
Amount: %2
Type: %3
Address: %4
</source>
        <translation>Дата: %1
Количество: %2
Тип: %3
Адрес: %4
</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="804"/>
        <source>Wallet is &lt;b&gt;encrypted&lt;/b&gt; and currently &lt;b&gt;unlocked&lt;/b&gt;</source>
        <translation>Бумажник &lt;b&gt;зашифрован&lt;/b&gt; и в настоящее время &lt;b&gt;разблокирован&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="812"/>
        <source>Wallet is &lt;b&gt;encrypted&lt;/b&gt; and currently &lt;b&gt;locked&lt;/b&gt;</source>
        <translation>Бумажник &lt;b&gt;зашифрован&lt;/b&gt; и в настоящее время &lt;b&gt;заблокирован&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="835"/>
        <source>Backup Wallet</source>
        <translation>Сделать резервную копию бумажника</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="835"/>
        <source>Wallet Data (*.dat)</source>
        <translation>Данные бумажника (*.dat)</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="838"/>
        <source>Backup Failed</source>
        <translation>Резервное копирование не удалось</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="838"/>
        <source>There was an error trying to save the wallet data to the new location.</source>
        <translation>При попытке сохранения данных бумажника в новое место произошла ошибка.</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="112"/>
        <source>A fatal error occured. Bitcoin can no longer continue safely and will quit.</source>
        <translation>Обнаружена неисправимая ошибка. Bitcoin не может продолжать безопасную работу и будет закрыт.</translation>
    </message>
</context>
<context>
    <name>ClientModel</name>
    <message>
        <location filename="../clientmodel.cpp" line="84"/>
        <source>Network Alert</source>
        <translation>Сетевая Тревога</translation>
    </message>
</context>
<context>
    <name>DisplayOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="246"/>
        <source>Display</source>
        <translation>Отображение</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="257"/>
        <source>default</source>
        <translation>по умолчанию</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="263"/>
        <source>The user interface language can be set here. This setting will only take effect after restarting Bitcoin.</source>
        <translation>Здесь можно выбрать язык интерфейса. Настройки вступят в силу после перезапуска Bitcoin.</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="252"/>
        <source>User Interface &amp;Language:</source>
        <translation>&amp;Язык интерфейса</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="273"/>
        <source>&amp;Unit to show amounts in:</source>
        <translation>&amp;Отображать суммы в единицах: </translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="277"/>
        <source>Choose the default subdivision unit to show in the interface, and when sending coins</source>
        <translation>Единица измерения количества монет при отображении и при отправке</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="284"/>
        <source>&amp;Display addresses in transaction list</source>
        <translation>&amp;Показывать адреса в списке транзакций</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="285"/>
        <source>Whether to show Bitcoin addresses in the transaction list</source>
        <translation>Показывать ли адреса Bitcoin в списке транзакций</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="303"/>
        <source>Warning</source>
        <translation>Внимание</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="303"/>
        <source>This setting will take effect after restarting Bitcoin.</source>
        <translation>Эта настройка вступит в силу после перезапуска Bitcoin</translation>
    </message>
</context>
<context>
    <name>EditAddressDialog</name>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="14"/>
        <source>Edit Address</source>
        <translation>Изменить адрес</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="25"/>
        <source>&amp;Label</source>
        <translation>&amp;Метка</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="35"/>
        <source>The label associated with this address book entry</source>
        <translation>Метка, связанная с данной записью</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="42"/>
        <source>&amp;Address</source>
        <translation>&amp;Адрес</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="52"/>
        <source>The address associated with this address book entry. This can only be modified for sending addresses.</source>
        <translation>Адрес, связанный с данной записью.</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="20"/>
        <source>New receiving address</source>
        <translation>Новый адрес для получения</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="24"/>
        <source>New sending address</source>
        <translation>Новый адрес для отправки</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="27"/>
        <source>Edit receiving address</source>
        <translation>Изменение адреса для получения</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="31"/>
        <source>Edit sending address</source>
        <translation>Изменение адреса для отправки</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="91"/>
        <source>The entered address &quot;%1&quot; is already in the address book.</source>
        <translation>Введённый адрес «%1» уже находится в адресной книге.</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="96"/>
        <source>The entered address &quot;%1&quot; is not a valid Bitcoin address.</source>
        <translation>Введённый адрес &quot;%1&quot; не является правильным Bitcoin-адресом.</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="101"/>
        <source>Could not unlock wallet.</source>
        <translation>Не удается разблокировать бумажник.</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="106"/>
        <source>New key generation failed.</source>
        <translation>Генерация нового ключа не удалась.</translation>
    </message>
</context>
<context>
    <name>HelpMessageBox</name>
    <message>
        <location filename="../bitcoin.cpp" line="133"/>
        <location filename="../bitcoin.cpp" line="143"/>
        <source>Bitcoin-Qt</source>
        <translation>Bitcoin-Qt</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="133"/>
        <source>version</source>
        <translation>версия</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="135"/>
        <source>Usage:</source>
        <translation>Использование:</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="136"/>
        <source>options</source>
        <translation>опции</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="138"/>
        <source>UI options</source>
        <translation>Опции интерфейса</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="139"/>
        <source>Set language, for example &quot;de_DE&quot; (default: system locale)</source>
        <translation>Выберите язык, например &quot;de_DE&quot; (по умолчанию: как в системе)</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="140"/>
        <source>Start minimized</source>
        <translation>Запускать свёрнутым</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="141"/>
        <source>Show splash screen on startup (default: 1)</source>
        <translation>Показывать сплэш при запуске (по умолчанию: 1)</translation>
    </message>
</context>
<context>
    <name>MainOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="227"/>
        <source>Detach block and address databases at shutdown. This means they can be moved to another data directory, but it slows down shutdown. The wallet is always detached.</source>
        <translation>Отключить базы данных блоков и адресов при выходе. Это означает, что их можно будет переместить в другой каталог данных, но завершение работы будет медленнее. Бумажник всегда отключается.</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="212"/>
        <source>Pay transaction &amp;fee</source>
        <translation>Добавлять ко&amp;миссию</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="204"/>
        <source>Main</source>
        <translation>Основное</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="206"/>
        <source>Optional transaction fee per kB that helps make sure your transactions are processed quickly. Most transactions are 1 kB. Fee 0.01 recommended.</source>
        <translation>Опциональная комиссия за каждый КБ транзакции, которая позволяет быть уверенным, что Ваша транзакция будет обработана быстро.  Большинство транзакций занимают 1КБ.  Рекомендуется комиссия 0.01.</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="222"/>
        <source>&amp;Start Bitcoin on system login</source>
        <translation>&amp;Запускать Bitcoin при входе в систему</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="223"/>
        <source>Automatically start Bitcoin after logging in to the system</source>
        <translation>Автоматически запускать Bitcoin после входа в систему</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="226"/>
        <source>&amp;Detach databases at shutdown</source>
        <translation>&amp;Отключать базы данных при выходе</translation>
    </message>
</context>
<context>
    <name>MessagePage</name>
    <message>
        <location filename="../forms/messagepage.ui" line="14"/>
        <source>Sign Message</source>
        <translation>Подписать сообщение</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="20"/>
        <source>You can sign messages with your addresses to prove you own them. Be careful not to sign anything vague, as phishing attacks may try to trick you into signing your identity over to them. Only sign fully-detailed statements you agree to.</source>
        <translation>Вы можете подписывать сообщения своими адресами, чтобы доказать владение ими. Будьте осторожны, не подписывайте что-то неопределённое, так как фишинговые атаки могут обманным путём заставить вас подписать нежелательные сообщения. Подписывайте только те сообщения, с которыми вы согласны вплоть до мелочей.</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="38"/>
        <source>The address to sign the message with  (e.g. **********************************)</source>
        <translation>Адрес, которым вы хотите подписать сообщение  (напр. **********************************)</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="48"/>
        <source>Choose adress from address book</source>
        <translation>Выбрать адрес из адресной книги</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="58"/>
        <source>Alt+A</source>
        <translation>Alt+A</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="71"/>
        <source>Paste address from clipboard</source>
        <translation>Вставить адрес из буфера обмена</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="81"/>
        <source>Alt+P</source>
        <translation>Alt+P</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="93"/>
        <source>Enter the message you want to sign here</source>
        <translation>Введите сообщение для подписи</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="128"/>
        <source>Copy the current signature to the system clipboard</source>
        <translation>Скопировать текущую подпись в системный буфер обмена</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="131"/>
        <source>&amp;Copy Signature</source>
        <translation>&amp;Копировать подпись</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="142"/>
        <source>Reset all sign message fields</source>
        <translation>Сбросить значения всех полей подписывания сообщений</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="145"/>
        <source>Clear &amp;All</source>
        <translation>Очистить &amp;всё</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="31"/>
        <source>Click &quot;Sign Message&quot; to get signature</source>
        <translation>Для создания подписи нажмите на &quot;Подписать сообщение&quot;</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="114"/>
        <source>Sign a message to prove you own this address</source>
        <translation>Подпишите сообщение для доказательства </translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="117"/>
        <source>&amp;Sign Message</source>
        <translation>&amp;Подписать сообщение</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="30"/>
        <source>Enter a Bitcoin address (e.g. **********************************)</source>
        <translation>Введите адрес Bitcoin (напр. **********************************)</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="83"/>
        <location filename="../messagepage.cpp" line="90"/>
        <location filename="../messagepage.cpp" line="105"/>
        <location filename="../messagepage.cpp" line="117"/>
        <source>Error signing</source>
        <translation>Ошибка создания подписи</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="83"/>
        <source>%1 is not a valid address.</source>
        <translation>%1 не является правильным адресом.</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="90"/>
        <source>%1 does not refer to a key.</source>
        <translation>%1 не связан с ключом</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="105"/>
        <source>Private key for %1 is not available.</source>
        <translation>Секретный ключ для %1 не доступен</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="117"/>
        <source>Sign failed</source>
        <translation>Подписание не удалось.</translation>
    </message>
</context>
<context>
    <name>NetworkOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="345"/>
        <source>Network</source>
        <translation>Сеть</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="347"/>
        <source>Map port using &amp;UPnP</source>
        <translation>Пробросить порт через &amp;UPnP</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="348"/>
        <source>Automatically open the Bitcoin client port on the router. This only works when your router supports UPnP and it is enabled.</source>
        <translation>Автоматически открыть порт для Bitcoin-клиента на роутере. Работает только если Ваш роутер поддерживает UPnP, и данная функция включена.</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="351"/>
        <source>&amp;Connect through SOCKS4 proxy:</source>
        <translation>&amp;Подключаться через SOCKS4 прокси:</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="352"/>
        <source>Connect to the Bitcon network through a SOCKS4 proxy (e.g. when connecting through Tor)</source>
        <translation>Подключаться к сети Bitcoin через прокси SOCKS4 (например, при использовании Tor)</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="357"/>
        <source>Proxy &amp;IP:</source>
        <translation>&amp;IP Прокси: </translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="366"/>
        <source>&amp;Port:</source>
        <translation>По&amp;рт: </translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="363"/>
        <source>IP address of the proxy (e.g. 127.0.0.1)</source>
        <translation>IP-адрес прокси (например 127.0.0.1)</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="372"/>
        <source>Port of the proxy (e.g. 1234)</source>
        <translation>Порт прокси-сервера (например 1234)</translation>
    </message>
</context>
<context>
    <name>OptionsDialog</name>
    <message>
        <location filename="../optionsdialog.cpp" line="135"/>
        <source>Options</source>
        <translation>Опции</translation>
    </message>
</context>
<context>
    <name>OverviewPage</name>
    <message>
        <location filename="../forms/overviewpage.ui" line="14"/>
        <source>Form</source>
        <translation>Форма</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="47"/>
        <location filename="../forms/overviewpage.ui" line="204"/>
        <source>The displayed information may be out of date. Your wallet automatically synchronizes with the Bitcoin network after a connection is established, but this process has not completed yet.</source>
        <translation>Отображаемая информация может быть устаревшей. Ваш бумажник автоматически синхронизируется с сетью Bitcoin после подключения, но этот процесс пока не завершён.</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="89"/>
        <source>Balance:</source>
        <translation>Баланс:</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="147"/>
        <source>Number of transactions:</source>
        <translation>Количество транзакций:</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="118"/>
        <source>Unconfirmed:</source>
        <translation>Не подтверждено:</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="40"/>
        <source>Wallet</source>
        <translation>Бумажник</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="197"/>
        <source>&lt;b&gt;Recent transactions&lt;/b&gt;</source>
        <translation>&lt;b&gt;Последние транзакции&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="105"/>
        <source>Your current balance</source>
        <translation>Ваш текущий баланс</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="134"/>
        <source>Total of transactions that have yet to be confirmed, and do not yet count toward the current balance</source>
        <translation>Общая сумма всех транзакций, которые до сих пор не подтверждены, и до сих пор не учитываются в текущем балансе</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="154"/>
        <source>Total number of transactions in wallet</source>
        <translation>Общее количество транзакций в Вашем бумажнике</translation>
    </message>
    <message>
        <location filename="../overviewpage.cpp" line="110"/>
        <location filename="../overviewpage.cpp" line="111"/>
        <source>out of sync</source>
        <translation>не синхронизировано</translation>
    </message>
</context>
<context>
    <name>QRCodeDialog</name>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="14"/>
        <source>QR Code Dialog</source>
        <translation>Диалог QR-кода</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="32"/>
        <source>QR Code</source>
        <translation>QR код</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="55"/>
        <source>Request Payment</source>
        <translation>Запросить платёж</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="70"/>
        <source>Amount:</source>
        <translation>Количество:</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="105"/>
        <source>BTC</source>
        <translation>BTC</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="121"/>
        <source>Label:</source>
        <translation>Метка:</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="144"/>
        <source>Message:</source>
        <translation>Сообщение:</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="186"/>
        <source>&amp;Save As...</source>
        <translation>&amp;Сохранить как...</translation>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="45"/>
        <source>Error encoding URI into QR Code.</source>
        <translation>Ошибка кодирования URI в QR-код</translation>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="63"/>
        <source>Resulting URI too long, try to reduce the text for label / message.</source>
        <translation>Получившийся URI слишком длинный, попробуйте сократить текст метки / сообщения.</translation>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="120"/>
        <source>Save QR Code</source>
        <translation>Сохранить QR-код</translation>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="120"/>
        <source>PNG Images (*.png)</source>
        <translation>PNG Изображения (*.png)</translation>
    </message>
</context>
<context>
    <name>RPCConsole</name>
    <message>
        <location filename="../forms/rpcconsole.ui" line="14"/>
        <source>Bitcoin debug window</source>
        <translation>Окно отладки Bitcoin</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="46"/>
        <source>Client name</source>
        <translation>Имя клиента</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="56"/>
        <location filename="../forms/rpcconsole.ui" line="79"/>
        <location filename="../forms/rpcconsole.ui" line="102"/>
        <location filename="../forms/rpcconsole.ui" line="125"/>
        <location filename="../forms/rpcconsole.ui" line="161"/>
        <location filename="../forms/rpcconsole.ui" line="214"/>
        <location filename="../forms/rpcconsole.ui" line="237"/>
        <location filename="../forms/rpcconsole.ui" line="260"/>
        <location filename="../rpcconsole.cpp" line="245"/>
        <source>N/A</source>
        <translation>Н/Д</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="69"/>
        <source>Client version</source>
        <translation>Версия клиента</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="24"/>
        <source>&amp;Information</source>
        <translation>&amp;Информация</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="39"/>
        <source>Client</source>
        <translation>Клиент</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="115"/>
        <source>Startup time</source>
        <translation>Время запуска</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="144"/>
        <source>Network</source>
        <translation>Сеть</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="151"/>
        <source>Number of connections</source>
        <translation>Число подключений</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="174"/>
        <source>On testnet</source>
        <translation>В тестовой сети</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="197"/>
        <source>Block chain</source>
        <translation>Цепь блоков</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="204"/>
        <source>Current number of blocks</source>
        <translation>Текущее число блоков</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="227"/>
        <source>Estimated total blocks</source>
        <translation>Расчётное число блоков</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="250"/>
        <source>Last block time</source>
        <translation>Время последнего блока</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="292"/>
        <source>Debug logfile</source>
        <translation>Отладочный лог-файл</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="299"/>
        <source>Open the Bitcoin debug logfile from the current data directory. This can take a few seconds for large logfiles.</source>
        <translation>Открыть отладочный лог-файл Bitcoin из текущего каталога данных. Это может занять несколько секунд для больших лог-файлов.</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="302"/>
        <source>&amp;Open</source>
        <translation>&amp;Открыть</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="323"/>
        <source>&amp;Console</source>
        <translation>Консоль</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="92"/>
        <source>Build date</source>
        <translation>Дата сборки</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="372"/>
        <source>Clear console</source>
        <translation>Очистить консоль</translation>
    </message>
    <message>
        <location filename="../rpcconsole.cpp" line="212"/>
        <source>Welcome to the Bitcoin RPC console.</source>
        <translation>Добро пожаловать в RPC-консоль Bitcoin.</translation>
    </message>
    <message>
        <location filename="../rpcconsole.cpp" line="213"/>
        <source>Use up and down arrows to navigate history, and &lt;b&gt;Ctrl-L&lt;/b&gt; to clear screen.</source>
        <translation>Используйте стрелки вверх и вниз для просмотра истории и &lt;b&gt;Ctrl-L&lt;/b&gt; для очистки экрана.</translation>
    </message>
    <message>
        <location filename="../rpcconsole.cpp" line="214"/>
        <source>Type &lt;b&gt;help&lt;/b&gt; for an overview of available commands.</source>
        <translation>Напишите &lt;b&gt;help&lt;/b&gt; для просмотра доступных команд.</translation>
    </message>
</context>
<context>
    <name>SendCoinsDialog</name>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="14"/>
        <location filename="../sendcoinsdialog.cpp" line="122"/>
        <location filename="../sendcoinsdialog.cpp" line="127"/>
        <location filename="../sendcoinsdialog.cpp" line="132"/>
        <location filename="../sendcoinsdialog.cpp" line="137"/>
        <location filename="../sendcoinsdialog.cpp" line="143"/>
        <location filename="../sendcoinsdialog.cpp" line="148"/>
        <location filename="../sendcoinsdialog.cpp" line="153"/>
        <source>Send Coins</source>
        <translation>Отправка</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="64"/>
        <source>Send to multiple recipients at once</source>
        <translation>Отправить нескольким получателям одновременно</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="67"/>
        <source>&amp;Add Recipient</source>
        <translation>&amp;Добавить получателя</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="84"/>
        <source>Remove all transaction fields</source>
        <translation>Удалить все поля транзакции</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="87"/>
        <source>Clear &amp;All</source>
        <translation>Очистить &amp;всё</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="106"/>
        <source>Balance:</source>
        <translation>Баланс:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="113"/>
        <source>123.456 BTC</source>
        <translation>123.456 BTC</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="144"/>
        <source>Confirm the send action</source>
        <translation>Подтвердить отправку</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="147"/>
        <source>&amp;Send</source>
        <translation>&amp;Отправить</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="94"/>
        <source>&lt;b&gt;%1&lt;/b&gt; to %2 (%3)</source>
        <translation>&lt;b&gt;%1&lt;/b&gt; адресату %2 (%3)</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="99"/>
        <source>Confirm send coins</source>
        <translation>Подтвердите отправку монет</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="100"/>
        <source>Are you sure you want to send %1?</source>
        <translation>Вы уверены, что хотите отправить %1?</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="100"/>
        <source> and </source>
        <translation> и </translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="123"/>
        <source>The recepient address is not valid, please recheck.</source>
        <translation>Адрес получателя неверный, пожалуйста, перепроверьте.</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="128"/>
        <source>The amount to pay must be larger than 0.</source>
        <translation>Количество монет для отправки должно быть больше 0.</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="133"/>
        <source>The amount exceeds your balance.</source>
        <translation>Количество отправляемых монет превышает Ваш баланс</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="138"/>
        <source>The total exceeds your balance when the %1 transaction fee is included.</source>
        <translation>Сумма превысит Ваш баланс, если комиссия в размере %1 будет добавлена к транзакции</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="144"/>
        <source>Duplicate address found, can only send to each address once per send operation.</source>
        <translation>Обнаружен дублирующийся адрес. Отправка на один и тот же адрес возможна только один раз за одну операцию отправки</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="149"/>
        <source>Error: Transaction creation failed.</source>
        <translation>Ошибка: не удалось создать транзакцию.</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="154"/>
        <source>Error: The transaction was rejected. This might happen if some of the coins in your wallet were already spent, such as if you used a copy of wallet.dat and coins were spent in the copy but not marked as spent here.</source>
        <translation>Ошибка: В транзакции отказано.  Такое может произойти, если некоторые монеты уже были потрачены, например, если Вы используете одну копию файла wallet.dat, а монеты были потрачены из другой копии, но не были отмечены как потраченные в этой.</translation>
    </message>
</context>
<context>
    <name>SendCoinsEntry</name>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="14"/>
        <source>Form</source>
        <translation>Форма</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="29"/>
        <source>A&amp;mount:</source>
        <translation>Ко&amp;личество:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="42"/>
        <source>Pay &amp;To:</source>
        <translation>Полу&amp;чатель:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="66"/>
        <location filename="../sendcoinsentry.cpp" line="25"/>
        <source>Enter a label for this address to add it to your address book</source>
        <translation>Введите метку для данного адреса (для добавления в адресную книгу)</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="75"/>
        <source>&amp;Label:</source>
        <translation>&amp;Метка:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="93"/>
        <source>The address to send the payment to  (e.g. **********************************)</source>
        <translation>Адрес получателя платежа (например **********************************)</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="103"/>
        <source>Choose address from address book</source>
        <translation>Выберите адрес из адресной книги</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="113"/>
        <source>Alt+A</source>
        <translation>Alt+A</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="120"/>
        <source>Paste address from clipboard</source>
        <translation>Вставить адрес из буфера обмена</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="130"/>
        <source>Alt+P</source>
        <translation>Alt+P</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="137"/>
        <source>Remove this recipient</source>
        <translation>Удалить этого получателя</translation>
    </message>
    <message>
        <location filename="../sendcoinsentry.cpp" line="26"/>
        <source>Enter a Bitcoin address (e.g. **********************************)</source>
        <translation>Введите Bitcoin-адрес (например **********************************)</translation>
    </message>
</context>
<context>
    <name>TransactionDesc</name>
    <message>
        <location filename="../transactiondesc.cpp" line="21"/>
        <source>Open for %1 blocks</source>
        <translation>Открыто до получения %1 блоков</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="23"/>
        <source>Open until %1</source>
        <translation>Открыто до %1</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="29"/>
        <source>%1/offline?</source>
        <translation>%1/оффлайн?</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="31"/>
        <source>%1/unconfirmed</source>
        <translation>%1/не подтверждено</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="33"/>
        <source>%1 confirmations</source>
        <translation>%1 подтверждений</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="51"/>
        <source>&lt;b&gt;Status:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Статус:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="56"/>
        <source>, has not been successfully broadcast yet</source>
        <translation>, ещё не было успешно разослано</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="58"/>
        <source>, broadcast through %1 node</source>
        <translation>, разослано через %1 узел</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="60"/>
        <source>, broadcast through %1 nodes</source>
        <translation>, разослано через %1 узлов</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="64"/>
        <source>&lt;b&gt;Date:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Дата:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="71"/>
        <source>&lt;b&gt;Source:&lt;/b&gt; Generated&lt;br&gt;</source>
        <translation>&lt;b&gt;Источник:&lt;/b&gt; [сгенерированно]&lt;br&gt;</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="77"/>
        <location filename="../transactiondesc.cpp" line="94"/>
        <source>&lt;b&gt;From:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Отправитель:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="94"/>
        <source>unknown</source>
        <translation>неизвестно</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="95"/>
        <location filename="../transactiondesc.cpp" line="118"/>
        <location filename="../transactiondesc.cpp" line="178"/>
        <source>&lt;b&gt;To:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Получатель:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="98"/>
        <source> (yours, label: </source>
        <translation> (Ваш, метка:</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="100"/>
        <source> (yours)</source>
        <translation> (ваш)</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="136"/>
        <location filename="../transactiondesc.cpp" line="150"/>
        <location filename="../transactiondesc.cpp" line="195"/>
        <location filename="../transactiondesc.cpp" line="212"/>
        <source>&lt;b&gt;Credit:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Кредит:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="138"/>
        <source>(%1 matures in %2 more blocks)</source>
        <translation>(%1 станет доступно через %2 блоков)</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="142"/>
        <source>(not accepted)</source>
        <translation>(не принято)</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="186"/>
        <location filename="../transactiondesc.cpp" line="194"/>
        <location filename="../transactiondesc.cpp" line="209"/>
        <source>&lt;b&gt;Debit:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Дебет:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="200"/>
        <source>&lt;b&gt;Transaction fee:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Комиссия:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="216"/>
        <source>&lt;b&gt;Net amount:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Общая сумма:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="222"/>
        <source>Message:</source>
        <translation>Сообщение:</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="224"/>
        <source>Comment:</source>
        <translation>Комментарий:</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="226"/>
        <source>Transaction ID:</source>
        <translation>Идентификатор транзакции:</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="229"/>
        <source>Generated coins must wait 120 blocks before they can be spent.  When you generated this block, it was broadcast to the network to be added to the block chain.  If it fails to get into the chain, it will change to &quot;not accepted&quot; and not be spendable.  This may occasionally happen if another node generates a block within a few seconds of yours.</source>
        <translation>Сгенерированные монеты должны подождать 120 блоков прежде, чем они смогут быть отправлены.  Когда Вы сгенерировали этот блок он был отправлен в сеть, чтобы он был добавлен к цепочке блоков.  Если данная процедура не удастся, статус изменится на «не подтверждено» и монеты будут непередаваемыми.  Такое может случайно происходить в случае, если другой узел сгенерирует блок на несколько секунд раньше.</translation>
    </message>
</context>
<context>
    <name>TransactionDescDialog</name>
    <message>
        <location filename="../forms/transactiondescdialog.ui" line="14"/>
        <source>Transaction details</source>
        <translation>Детали транзакции</translation>
    </message>
    <message>
        <location filename="../forms/transactiondescdialog.ui" line="20"/>
        <source>This pane shows a detailed description of the transaction</source>
        <translation>Данный диалог показывает детализированную статистику по выбранной транзакции</translation>
    </message>
</context>
<context>
    <name>TransactionTableModel</name>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Date</source>
        <translation>Дата</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Type</source>
        <translation>Тип</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Address</source>
        <translation>Адрес</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Amount</source>
        <translation>Количество</translation>
    </message>
    <message numerus="yes">
        <location filename="../transactiontablemodel.cpp" line="281"/>
        <source>Open for %n block(s)</source>
        <translation><numerusform>Открыто для %n блока</numerusform><numerusform>Открыто для %n блоков</numerusform><numerusform>Открыто для %n блоков</numerusform></translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="284"/>
        <source>Open until %1</source>
        <translation>Открыто до %1</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="287"/>
        <source>Offline (%1 confirmations)</source>
        <translation>Оффлайн (%1 подтверждений)</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="290"/>
        <source>Unconfirmed (%1 of %2 confirmations)</source>
        <translation>Не подтверждено (%1 из %2 подтверждений)</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="293"/>
        <source>Confirmed (%1 confirmations)</source>
        <translation>Подтверждено (%1 подтверждений)</translation>
    </message>
    <message numerus="yes">
        <location filename="../transactiontablemodel.cpp" line="301"/>
        <source>Mined balance will be available in %n more blocks</source>
        <translation><numerusform>Добытыми монетами можно будет воспользоваться через %n блок</numerusform><numerusform>Добытыми монетами можно будет воспользоваться через %n блока</numerusform><numerusform>Добытыми монетами можно будет воспользоваться через %n блоков</numerusform></translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="307"/>
        <source>This block was not received by any other nodes and will probably not be accepted!</source>
        <translation>Этот блок не был получен другими узлами и, возможно, не будет принят!</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="310"/>
        <source>Generated but not accepted</source>
        <translation>Сгенерированно, но не подтверждено</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="353"/>
        <source>Received with</source>
        <translation>Получено</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="355"/>
        <source>Received from</source>
        <translation>Получено от</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="358"/>
        <source>Sent to</source>
        <translation>Отправлено</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="360"/>
        <source>Payment to yourself</source>
        <translation>Отправлено себе</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="362"/>
        <source>Mined</source>
        <translation>Добыто</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="400"/>
        <source>(n/a)</source>
        <translation>[не доступно]</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="599"/>
        <source>Transaction status. Hover over this field to show number of confirmations.</source>
        <translation>Статус транзакции. Подведите курсор к нужному полю для того, чтобы увидеть количество подтверждений.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="601"/>
        <source>Date and time that the transaction was received.</source>
        <translation>Дата и время, когда транзакция была получена.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="603"/>
        <source>Type of transaction.</source>
        <translation>Тип транзакции.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="605"/>
        <source>Destination address of transaction.</source>
        <translation>Адрес назначения транзакции.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="607"/>
        <source>Amount removed from or added to balance.</source>
        <translation>Сумма, добавленная, или снятая с баланса.</translation>
    </message>
</context>
<context>
    <name>TransactionView</name>
    <message>
        <location filename="../transactionview.cpp" line="55"/>
        <location filename="../transactionview.cpp" line="71"/>
        <source>All</source>
        <translation>Все</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="56"/>
        <source>Today</source>
        <translation>Сегодня</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="57"/>
        <source>This week</source>
        <translation>На этой неделе</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="58"/>
        <source>This month</source>
        <translation>В этом месяце</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="59"/>
        <source>Last month</source>
        <translation>За последний месяц</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="60"/>
        <source>This year</source>
        <translation>В этом году</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="61"/>
        <source>Range...</source>
        <translation>Промежуток...</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="72"/>
        <source>Received with</source>
        <translation>Получено на</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="74"/>
        <source>Sent to</source>
        <translation>Отправлено на</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="76"/>
        <source>To yourself</source>
        <translation>Отправленные себе</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="77"/>
        <source>Mined</source>
        <translation>Добытые</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="78"/>
        <source>Other</source>
        <translation>Другое</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="85"/>
        <source>Enter address or label to search</source>
        <translation>Введите адрес или метку для поиска</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="92"/>
        <source>Min amount</source>
        <translation>Мин. сумма</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="126"/>
        <source>Copy address</source>
        <translation>Копировать адрес</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="127"/>
        <source>Copy label</source>
        <translation>Копировать метку</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="128"/>
        <source>Copy amount</source>
        <translation>Скопировать сумму</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="129"/>
        <source>Edit label</source>
        <translation>Изменить метку</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="130"/>
        <source>Show transaction details</source>
        <translation>Показать подробности транзакции</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="270"/>
        <source>Export Transaction Data</source>
        <translation>Экспортировать данные транзакций</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="271"/>
        <source>Comma separated file (*.csv)</source>
        <translation>Текст, разделённый запятыми (*.csv)</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="279"/>
        <source>Confirmed</source>
        <translation>Подтверждено</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="280"/>
        <source>Date</source>
        <translation>Дата</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="281"/>
        <source>Type</source>
        <translation>Тип</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="282"/>
        <source>Label</source>
        <translation>Метка</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="283"/>
        <source>Address</source>
        <translation>Адрес</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="284"/>
        <source>Amount</source>
        <translation>Количество</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="285"/>
        <source>ID</source>
        <translation>ID</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="289"/>
        <source>Error exporting</source>
        <translation>Ошибка экспорта</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="289"/>
        <source>Could not write to file %1.</source>
        <translation>Невозможно записать в файл %1.</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="384"/>
        <source>Range:</source>
        <translation>Промежуток от:</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="392"/>
        <source>to</source>
        <translation>до</translation>
    </message>
</context>
<context>
    <name>VerifyMessageDialog</name>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="14"/>
        <source>Verify Signed Message</source>
        <translation>Проверить подписанное сообщение</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="20"/>
        <source>Enter the message and signature below (be careful to correctly copy newlines, spaces, tabs and other invisible characters) to obtain the Bitcoin address used to sign the message.</source>
        <translation>Введите ниже сообщение и подпись (копируйте в точности переводы строк, пробелы, табы и иные невидимые или малозаметные символы), чтобы получить адрес Bitcoin, который использовался при подписывании сообщения.</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="62"/>
        <source>Verify a message and obtain the Bitcoin address used to sign the message</source>
        <translation>Проверить сообщение и получить адрес Bitcoin, который использовался для подписи</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="65"/>
        <source>&amp;Verify Message</source>
        <translation>&amp;Проверить сообщение</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="79"/>
        <source>Copy the currently selected address to the system clipboard</source>
        <translation>Скопировать выбранный адрес в системный буфер обмена</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="82"/>
        <source>&amp;Copy Address</source>
        <translation>&amp;Копировать адрес</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="93"/>
        <source>Reset all verify message fields</source>
        <translation>Сбросить все поля проверки сообщения</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="96"/>
        <source>Clear &amp;All</source>
        <translation>Очистить &amp;всё</translation>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="28"/>
        <source>Enter Bitcoin signature</source>
        <translation>Введите подпись Bitcoin</translation>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="29"/>
        <source>Click &quot;Verify Message&quot; to obtain address</source>
        <translation>Щёлкните &quot;Проверить сообщение&quot; для получения адреса</translation>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="55"/>
        <location filename="../verifymessagedialog.cpp" line="62"/>
        <source>Invalid Signature</source>
        <translation>Неверная подпись</translation>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="55"/>
        <source>The signature could not be decoded. Please check the signature and try again.</source>
        <translation>Не удаётся декодировать подпись. Проверьте подпись и попробуйте ещё раз.</translation>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="62"/>
        <source>The signature did not match the message digest. Please check the signature and try again.</source>
        <translation>Подпись не совпадает с контрольной суммой сообщения. Проверьте подпись и попробуйте ещё раз.</translation>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="72"/>
        <source>Address not found in address book.</source>
        <translation>Адрес не найден в адресной книге.</translation>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="72"/>
        <source>Address found in address book: %1</source>
        <translation>Адрес найден в адресной книге: %1</translation>
    </message>
</context>
<context>
    <name>WalletModel</name>
    <message>
        <location filename="../walletmodel.cpp" line="158"/>
        <source>Sending...</source>
        <translation>Отправка....</translation>
    </message>
</context>
<context>
    <name>WindowOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="313"/>
        <source>Window</source>
        <translation>Окно</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="316"/>
        <source>&amp;Minimize to the tray instead of the taskbar</source>
        <translation>&amp;Cворачивать в системный лоток вместо панели задач</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="317"/>
        <source>Show only a tray icon after minimizing the window</source>
        <translation>Показывать только иконку в системном лотке после сворачивания окна</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="320"/>
        <source>M&amp;inimize on close</source>
        <translation>С&amp;ворачивать при закрытии</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="321"/>
        <source>Minimize instead of exit the application when the window is closed. When this option is enabled, the application will be closed only after selecting Quit in the menu.</source>
        <translation>Сворачивать вместо выхода, когда окно приложения закрывается. Если данная опция будет выбрана, приложение закроется только после выбора пункта Выход в меню.</translation>
    </message>
</context>
<context>
    <name>bitcoin-core</name>
    <message>
        <location filename="../bitcoinstrings.cpp" line="43"/>
        <source>Bitcoin version</source>
        <translation>Версия</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="44"/>
        <source>Usage:</source>
        <translation>Использование:</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="45"/>
        <source>Send command to -server or bitcoind</source>
        <translation>Отправить команду на -server или bitcoind</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="46"/>
        <source>List commands</source>
        <translation>Список команд
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="47"/>
        <source>Get help for a command</source>
        <translation>Получить помощь по команде</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="49"/>
        <source>Options:</source>
        <translation>Опции:</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="50"/>
        <source>Specify configuration file (default: bitcoin.conf)</source>
        <translation>Указать конфигурационный файл (по умолчанию: bitcoin.conf)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="51"/>
        <source>Specify pid file (default: bitcoind.pid)</source>
        <translation>Указать pid-файл (по умолчанию: bitcoin.pid)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="52"/>
        <source>Generate coins</source>
        <translation>Генерировать монеты</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="53"/>
        <source>Don&apos;t generate coins</source>
        <translation>Не генерировать монеты</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="54"/>
        <source>Specify data directory</source>
        <translation>Укажите каталог данных</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="55"/>
        <source>Set database cache size in megabytes (default: 25)</source>
        <translation>Установить размер кэша базы данных в мегабайтах (по умолчанию: 25)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="56"/>
        <source>Set database disk log size in megabytes (default: 100)</source>
        <translation>Установить размер лога базы данных в мегабайтах (по умолчанию: 100)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="57"/>
        <source>Specify connection timeout (in milliseconds)</source>
        <translation>Укажите таймаут соединения (в миллисекундах)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="63"/>
        <source>Listen for connections on &lt;port&gt; (default: 8333 or testnet: 18333)</source>
        <translation>Принимать входящие подключения на &lt;port&gt; (по умолчанию: 8333 или 18333 в тестовой сети)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="64"/>
        <source>Maintain at most &lt;n&gt; connections to peers (default: 125)</source>
        <translation>Поддерживать не более &lt;n&gt; подключений к узлам (по умолчанию: 125)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="66"/>
        <source>Connect only to the specified node</source>
        <translation>Подключаться только к указанному узлу</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="67"/>
        <source>Connect to a node to retrieve peer addresses, and disconnect</source>
        <translation>Подключиться к узлу, чтобы получить список адресов других участников и отключиться</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="68"/>
        <source>Specify your own public address</source>
        <translation>Укажите ваш собственный публичный адрес</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="69"/>
        <source>Only connect to nodes in network &lt;net&gt; (IPv4 or IPv6)</source>
        <translation>Подключаться только к узлам из сети &lt;net&gt; (IPv4 или IPv6)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="70"/>
        <source>Try to discover public IP address (default: 1)</source>
        <translation>Попробовать обнаружить внешний IP-адрес (по умолчанию: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="73"/>
        <source>Bind to given address. Use [host]:port notation for IPv6</source>
        <translation>Привязаться (bind) к указанному адресу. Используйте запись вида [хост]:порт для IPv6</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="75"/>
        <source>Threshold for disconnecting misbehaving peers (default: 100)</source>
        <translation>Порог для отключения неправильно ведущих себя узлов (по умолчанию: 100)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="76"/>
        <source>Number of seconds to keep misbehaving peers from reconnecting (default: 86400)</source>
        <translation>Число секунд блокирования неправильно ведущих себя узлов (по умолчанию: 86400)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="79"/>
        <source>Maximum per-connection receive buffer, &lt;n&gt;*1000 bytes (default: 10000)</source>
        <translation>Максимальный размер буфера приёма на соединение, &lt;n&gt;*1000 байт (по умолчанию: 10000)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="80"/>
        <source>Maximum per-connection send buffer, &lt;n&gt;*1000 bytes (default: 10000)</source>
        <translation>Максимальный размер буфера отправки на соединение, &lt;n&gt;*1000 байт (по умолчанию: 10000)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="83"/>
        <source>Detach block and address databases. Increases shutdown time (default: 0)</source>
        <translation>Отключить базы данных блоков и адресов. Увеличивает время завершения работы (по умолчанию: 0)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="86"/>
        <source>Accept command line and JSON-RPC commands</source>
        <translation>Принимать командную строку и команды JSON-RPC</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="87"/>
        <source>Run in the background as a daemon and accept commands</source>
        <translation>Запускаться в фоне как демон и принимать команды</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="88"/>
        <source>Use the test network</source>
        <translation>Использовать тестовую сеть</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="89"/>
        <source>Output extra debugging information</source>
        <translation>Выводить дополнительную отладочную информацию</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="90"/>
        <source>Prepend debug output with timestamp</source>
        <translation>Дописывать отметки времени к отладочному выводу</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="91"/>
        <source>Send trace/debug info to console instead of debug.log file</source>
        <translation>Выводить информацию трассировки/отладки на консоль вместо файла debug.log</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="92"/>
        <source>Send trace/debug info to debugger</source>
        <translation>Отправлять информацию трассировки/отладки в отладчик</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="93"/>
        <source>Username for JSON-RPC connections</source>
        <translation>Имя для подключений JSON-RPC</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="94"/>
        <source>Password for JSON-RPC connections</source>
        <translation>Пароль для подключений JSON-RPC</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="95"/>
        <source>Listen for JSON-RPC connections on &lt;port&gt; (default: 8332)</source>
        <translation>Ожидать подключения JSON-RPC на &lt;порт&gt; (по умолчанию: 8332)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="96"/>
        <source>Allow JSON-RPC connections from specified IP address</source>
        <translation>Разрешить подключения JSON-RPC с указанного IP</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="97"/>
        <source>Send commands to node running on &lt;ip&gt; (default: 127.0.0.1)</source>
        <translation>Посылать команды узлу, запущенному на &lt;ip&gt; (по умолчанию: 127.0.0.1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="98"/>
        <source>Execute command when the best block changes (%s in cmd is replaced by block hash)</source>
        <translation>Выполнить команду, когда появляется новый блок (%s в команде заменяется на хэш блока)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="101"/>
        <source>Upgrade wallet to latest format</source>
        <translation>Обновить бумажник до последнего формата</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="102"/>
        <source>Set key pool size to &lt;n&gt; (default: 100)</source>
        <translation>Установить размер запаса ключей в &lt;n&gt; (по умолчанию: 100)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="103"/>
        <source>Rescan the block chain for missing wallet transactions</source>
        <translation>Перепроверить цепь блоков на предмет отсутствующих в бумажнике транзакций</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="104"/>
        <source>How many blocks to check at startup (default: 2500, 0 = all)</source>
        <translation>Сколько блоков проверять при запуске (по умолчанию: 2500, 0 = все)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="105"/>
        <source>How thorough the block verification is (0-6, default: 1)</source>
        <translation>Насколько тщательно проверять блоки (0-6, по умолчанию: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="106"/>
        <source>Imports blocks from external blk000?.dat file</source>
        <translation>Импортировать блоки из внешнего файла blk000?.dat</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="108"/>
        <source>
SSL options: (see the Bitcoin Wiki for SSL setup instructions)</source>
        <translation>
Параметры SSL: (см. Bitcoin Wiki для инструкций по настройке SSL)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="111"/>
        <source>Use OpenSSL (https) for JSON-RPC connections</source>
        <translation>Использовать OpenSSL (https) для подключений JSON-RPC</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="112"/>
        <source>Server certificate file (default: server.cert)</source>
        <translation>Файл серверного сертификата (по умолчанию: server.cert)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="113"/>
        <source>Server private key (default: server.pem)</source>
        <translation>Приватный ключ сервера (по умолчанию: server.pem)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="114"/>
        <source>Acceptable ciphers (default: TLSv1+HIGH:!SSLv2:!aNULL:!eNULL:!AH:!3DES:@STRENGTH)</source>
        <translation>Разрешённые алгоритмы (по умолчанию: TLSv1+HIGH:!SSLv2:!aNULL:!eNULL:!AH:!3DES:@STRENGTH)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="145"/>
        <source>Warning: Disk space is low</source>
        <translation>ВНИМАНИЕ: мало места на диске</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="107"/>
        <source>This help message</source>
        <translation>Эта справка</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="121"/>
        <source>Cannot obtain a lock on data directory %s.  Bitcoin is probably already running.</source>
        <translation>Невозможно установить блокировку на рабочую директорию %s.  Возможно, бумажник уже запущен.</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="48"/>
        <source>Bitcoin</source>
        <translation>Биткоин</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="30"/>
        <source>Unable to bind to %s on this computer (bind returned error %d, %s)</source>
        <translation>Невозможно привязаться к %s на этом компьютере (bind вернул ошибку %d, %s)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="58"/>
        <source>Connect through socks proxy</source>
        <translation>Подключаться через socks прокси</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="59"/>
        <source>Select the version of socks proxy to use (4 or 5, 5 is default)</source>
        <translation>Выберите версию socks прокси (4 или 5, по умолчанию 5)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="60"/>
        <source>Do not use proxy for connections to network &lt;net&gt; (IPv4 or IPv6)</source>
        <translation>Не использовать прокси для подключения к сети &lt;net&gt; (IPv4 или IPv6)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="61"/>
        <source>Allow DNS lookups for -addnode, -seednode and -connect</source>
        <translation>Разрешить поиск в DNS для -addnode, -seednode и -connect</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="62"/>
        <source>Pass DNS requests to (SOCKS5) proxy</source>
        <translation>Выполнять DNS-запросы через (SOCKS5) прокси</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="142"/>
        <source>Loading addresses...</source>
        <translation>Загрузка адресов...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="132"/>
        <source>Error loading blkindex.dat</source>
        <translation>Ошибка чтения blkindex.dat</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="134"/>
        <source>Error loading wallet.dat: Wallet corrupted</source>
        <translation>Ошибка загрузки wallet.dat: Бумажник поврежден</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="135"/>
        <source>Error loading wallet.dat: Wallet requires newer version of Bitcoin</source>
        <translation>Ошибка загрузки wallet.dat: бумажник требует более новую версию Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="136"/>
        <source>Wallet needed to be rewritten: restart Bitcoin to complete</source>
        <translation>Необходимо перезаписать бумажник, перезапустите Bitcoin для завершения операции.</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="137"/>
        <source>Error loading wallet.dat</source>
        <translation>Ошибка при загрузке wallet.dat</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="124"/>
        <source>Invalid -proxy address: &apos;%s&apos;</source>
        <translation>Неверный адрес -proxy: &apos;%s&apos;</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="125"/>
        <source>Unknown network specified in -noproxy: &apos;%s&apos;</source>
        <translation>В параметре -noproxy указана неизвестная сеть: &apos;%s&apos;</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="127"/>
        <source>Unknown network specified in -onlynet: &apos;%s&apos;</source>
        <translation>В параметре -onlynet указана неизвестная сеть: &apos;%s&apos;</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="126"/>
        <source>Unknown -socks proxy version requested: %i</source>
        <translation>В параметре -socks запрошена неизвестная версия: %i</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="128"/>
        <source>Cannot resolve -bind address: &apos;%s&apos;</source>
        <translation>Не удаётся разрешить адрес в параметре -bind: &apos;%s&apos;</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="129"/>
        <source>Not listening on any port</source>
        <translation>Никакие порты не прослушиваются</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="130"/>
        <source>Cannot resolve -externalip address: &apos;%s&apos;</source>
        <translation>Не удаётся разрешить адрес в параметре -externalip: &apos;%s&apos;</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="117"/>
        <source>Invalid amount for -paytxfee=&lt;amount&gt;: &apos;%s&apos;</source>
        <translation>Неверное количество в параметре -paytxfee=&lt;кол-во&gt;: &apos;%s&apos;</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="143"/>
        <source>Error: could not start node</source>
        <translation>Ошибка: не удалось запустить узел</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="31"/>
        <source>Error: Wallet locked, unable to create transaction  </source>
        <translation>Ошибка: бумажник заблокирован, невозможно создать транзакцию  </translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="32"/>
        <source>Error: This transaction requires a transaction fee of at least %s because of its amount, complexity, or use of recently received funds  </source>
        <translation>Ошибка: эта транзакция требует комиссию в размере как минимум %s из-за её объёма, сложности или использования недавно полученных средств  </translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="35"/>
        <source>Error: Transaction creation failed  </source>
        <translation>Ошибка: Создание транзакции не удалось  </translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="36"/>
        <source>Sending...</source>
        <translation>Отправка...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="37"/>
        <source>Error: The transaction was rejected.  This might happen if some of the coins in your wallet were already spent, such as if you used a copy of wallet.dat and coins were spent in the copy but not marked as spent here.</source>
        <translation>Ошибка: В транзакции отказано.  Такое может произойти, если некоторые монеты уже были потрачены, например, если Вы используете одну копию файла wallet.dat, а монеты были потрачены из другой копии, но не были отмечены как потраченные в этой.</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="41"/>
        <source>Invalid amount</source>
        <translation>Неверное количество</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="42"/>
        <source>Insufficient funds</source>
        <translation>Недостаточно монет</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="131"/>
        <source>Loading block index...</source>
        <translation>Загрузка индекса блоков...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="65"/>
        <source>Add a node to connect to and attempt to keep the connection open</source>
        <translation>Добавить узел для подключения и пытаться поддерживать соединение открытым</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="28"/>
        <source>Unable to bind to %s on this computer. Bitcoin is probably already running.</source>
        <translation>Невозможно привязаться к %s на этом компьютере.  Возможно, Bitcoin уже работает.</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="71"/>
        <source>Find peers using internet relay chat (default: 0)</source>
        <translation>Найти участников через IRC (по умолчанию: 0)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="72"/>
        <source>Accept connections from outside (default: 1)</source>
        <translation>Принимать входящие подключения (по умолчанию: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="74"/>
        <source>Find peers using DNS lookup (default: 1)</source>
        <translation>Найти участников с помощью запросов DNS (по умолчанию: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="81"/>
        <source>Use Universal Plug and Play to map the listening port (default: 1)</source>
        <translation>Использовать универсальный Plug and Play для проброса порта (по умолчанию: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="82"/>
        <source>Use Universal Plug and Play to map the listening port (default: 0)</source>
        <translation>Использовать универсальный Plug and Play для проброса порта (по умолчанию: 0)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="85"/>
        <source>Fee per KB to add to transactions you send</source>
        <translation>Комиссия на килобайт, добавляемая к вашим транзакциям</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="118"/>
        <source>Warning: -paytxfee is set very high. This is the transaction fee you will pay if you send a transaction.</source>
        <translation>ВНИМАНИЕ: установлена очень большое значение -paytxfee. Это комиссия, которую вы заплатите при проведении транзакции.</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="133"/>
        <source>Loading wallet...</source>
        <translation>Загрузка бумажника...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="138"/>
        <source>Cannot downgrade wallet</source>
        <translation>Не удаётся понизить версию бумажника</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="139"/>
        <source>Cannot initialize keypool</source>
        <translation>Не удаётся инициализировать массив ключей</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="140"/>
        <source>Cannot write default address</source>
        <translation>Не удаётся записать адрес по умолчанию</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="141"/>
        <source>Rescanning...</source>
        <translation>Сканирование...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="144"/>
        <source>Done loading</source>
        <translation>Загрузка завершена</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="8"/>
        <source>To use the %s option</source>
        <translation>Чтобы использовать опцию %s</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="9"/>
        <source>%s, you must set a rpcpassword in the configuration file:
 %s
It is recommended you use the following random password:
rpcuser=bitcoinrpc
rpcpassword=%s
(you do not need to remember this password)
If the file does not exist, create it with owner-readable-only file permissions.
</source>
        <translation>%s, вы должны установить опцию rpcpassword в конфигурационном файле:
 %s
Рекомендуется использовать следующий случайный пароль:
rpcuser=bitcoinrpc
rpcpassword=%s
(вам не нужно запоминать этот пароль)
Если файл не существует, создайте его и установите право доступа только для чтения только для владельца.
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="18"/>
        <source>Error</source>
        <translation>Ошибка</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="19"/>
        <source>An error occured while setting up the RPC port %i for listening: %s</source>
        <translation>Произошла ошибка в процессе открытия RPC-порта %i для прослушивания: %s</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="20"/>
        <source>You must set rpcpassword=&lt;password&gt; in the configuration file:
%s
If the file does not exist, create it with owner-readable-only file permissions.</source>
        <translation>Вы должны установить rpcpassword=&lt;password&gt; в конфигурационном файле:
%s
Если файл не существует, создайте его и установите право доступа только для чтения только для владельца.</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="25"/>
        <source>Warning: Please check that your computer&apos;s date and time are correct.  If your clock is wrong Bitcoin will not work properly.</source>
        <translation>ВНИМАНИЕ: Проверьте дату и время, установленные на Вашем компьютере. Если Ваши часы идут не правильно Bitcoin может наботать не корректно.</translation>
    </message>
</context>
</TS>