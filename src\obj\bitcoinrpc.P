obj/bitcoinrpc.o: bitcoinrpc.cpp main.h bignum.h util.h uint256.h \
 netbase.h serialize.h allocators.h version.h compat.h net.h mruset.h \
 protocol.h addrman.h sync.h key.h script.h keystore.h crypter.h db.h \
 scrypt.h wallet.h ui_interface.h walletdb.h base58.h init.h bitcoinrpc.h \
 json/json_spirit_reader_template.h json/json_spirit_value.h \
 json/json_spirit_error_position.h json/json_spirit_writer_template.h \
 json/json_spirit_utils.h
bitcoinrpc.cpp main.h bignum.h util.h uint256.h :
 netbase.h serialize.h allocators.h version.h compat.h net.h mruset.h :
 protocol.h addrman.h sync.h key.h script.h keystore.h crypter.h db.h :
 scrypt.h wallet.h ui_interface.h walletdb.h base58.h init.h bitcoinrpc.h :
 json/json_spirit_reader_template.h json/json_spirit_value.h :
 json/json_spirit_error_position.h json/json_spirit_writer_template.h :
 json/json_spirit_utils.h :
