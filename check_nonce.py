import hashlib
import base58
from ecdsa import Signing<PERSON><PERSON>, SECP256k1
import binascii

def int_to_private_key(n):
    """Convert an integer to a private key"""
    return n.to_bytes(32, byteorder='big').hex()

# The nonce from the genesis block
nonce = 44481

# Convert to private key format
private_key_hex = int_to_private_key(nonce)
print(f"Private key from nonce: {private_key_hex}")

# Try nonce with different encodings
nonce_le = nonce.to_bytes(4, byteorder='little').hex().ljust(64, '0')
nonce_be = nonce.to_bytes(4, byteorder='big').hex().ljust(64, '0')
print(f"Nonce as LE bytes padded: {nonce_le}")
print(f"Nonce as BE bytes padded: {nonce_be}")