obj/rpcnet.o: rpcnet.cpp net.h mruset.h netbase.h serialize.h \
 allocators.h version.h compat.h protocol.h uint256.h addrman.h util.h \
 sync.h bitcoinrpc.h json/json_spirit_reader_template.h \
 json/json_spirit_value.h json/json_spirit_error_position.h \
 json/json_spirit_writer_template.h json/json_spirit_utils.h
rpcnet.cpp net.h mruset.h netbase.h serialize.h :
 allocators.h version.h compat.h protocol.h uint256.h addrman.h util.h :
 sync.h bitcoinrpc.h json/json_spirit_reader_template.h :
 json/json_spirit_value.h json/json_spirit_error_position.h :
 json/json_spirit_writer_template.h json/json_spirit_utils.h :
