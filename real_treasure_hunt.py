#!/usr/bin/env python3
"""
REAL BELLSCOIN TREASURE HUNT
===========================

Now that we know the public key 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
is just a standard Litecoin network parameter, let's search for the REAL hidden private key
that allegedly unlocked <PERSON><PERSON><PERSON><PERSON>'s launchpad.

Looking for:
1. Unusual hex strings in the codebase
2. Hidden patterns in comments
3. Encoded data that might contain secrets
4. Combinations of "Nintondo" with other clues
"""

import hashlib
import binascii
import re
import os
from ecdsa import SigningKey, SECP256k1
from ecdsa.util import string_to_number, number_to_string

def sha256_hash(data: str) -> str:
    """Calculate SHA256 hash of input string"""
    return hashlib.sha256(data.encode('utf-8')).hexdigest()

def test_private_key_validity(private_key_hex: str) -> bool:
    """Test if a hex string is a valid private key (without checking against a target)"""
    try:
        if len(private_key_hex) != 64:
            private_key_hex = private_key_hex.zfill(64)

        private_key_int = int(private_key_hex, 16)
        if private_key_int == 0 or private_key_int >= SECP256k1.order:
            return False

        # Generate public key to verify it works
        private_key_bytes = number_to_string(private_key_int, SECP256k1.order)
        signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        verifying_key = signing_key.get_verifying_key()

        return True
    except:
        return False

def get_public_key_from_private(private_key_hex: str) -> str:
    """Get the public key from a private key"""
    try:
        if len(private_key_hex) != 64:
            private_key_hex = private_key_hex.zfill(64)

        private_key_int = int(private_key_hex, 16)
        private_key_bytes = number_to_string(private_key_int, SECP256k1.order)
        signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        verifying_key = signing_key.get_verifying_key()

        # Uncompressed format: 04 + x + y coordinates
        uncompressed_pubkey = "04" + verifying_key.to_string().hex()
        return uncompressed_pubkey
    except:
        return None

def scan_for_hex_strings():
    """Scan the codebase for unusual hex strings that could be private keys"""
    print("🔍 SCANNING FOR UNUSUAL HEX STRINGS")
    print("=" * 50)

    hex_candidates = set()

    # File extensions to scan
    extensions = ['.cpp', '.h', '.c', '.py', '.sh', '.txt', '.md']

    for root, dirs, files in os.walk('.'):
        if any(skip in root for skip in ['.git', '__pycache__', 'obj']):
            continue

        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                filepath = os.path.join(root, file)
                try:
                    with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                        # Look for 64-character hex strings (potential private keys)
                        hex_pattern = r'\b[0-9a-fA-F]{64}\b'
                        matches = re.findall(hex_pattern, content)
                        for match in matches:
                            # Skip the known Litecoin public key
                            if match.lower() != "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9".lower():
                                hex_candidates.add(match)

                        # Look for 32-character hex strings (half private keys)
                        hex_pattern_32 = r'\b[0-9a-fA-F]{32}\b'
                        matches_32 = re.findall(hex_pattern_32, content)
                        hex_candidates.update(matches_32)

                        # Look for quoted hex strings
                        quoted_hex = r'["\']([0-9a-fA-F]{32,64})["\']'
                        quoted_matches = re.findall(quoted_hex, content)
                        hex_candidates.update(quoted_matches)

                except:
                    continue

    return hex_candidates

def analyze_comment_hex():
    """Analyze the long hex string from the commented genesis block"""
    print("\n🔍 ANALYZING COMMENT HEX STRING")
    print("=" * 50)

    # The hex string from the comment in main.cpp
    comment_hex = "04ffff001d01044cd14d61792032322c20323031332c2031323a313620612e6d2e204544543a204a6170616e9273204e696b6b65692053746f636b2041766572616765204a503a4e494b202b312e3737252c20776869636820656e6465642061742074686569722068696768657374206c6576656c20696e206d6f7265207468616e206669766520796561727320696e2065616368206f6620746865206c6173742074687265652074726164696e672073657373696f6e732c20636c696d6265642061206675727468657220312e3225205765646e6573646179"

    print(f"Comment hex length: {len(comment_hex)} characters")

    # Decode the hex to see what it says
    try:
        decoded_bytes = binascii.unhexlify(comment_hex)
        decoded_text = decoded_bytes.decode('utf-8', errors='ignore')
        print(f"Decoded text: {decoded_text}")
    except:
        print("Could not decode comment hex")

    # Test various transformations of this hex string
    candidates = [
        ("Comment hex hash", sha256_hash(comment_hex)),
        ("Comment text hash", sha256_hash(decoded_text) if 'decoded_text' in locals() else None),
        ("Nintondo + comment hash", sha256_hash("Nintondo" + comment_hex)),
    ]

    for name, candidate in candidates:
        if candidate and test_private_key_validity(candidate):
            pubkey = get_public_key_from_private(candidate)
            print(f"🎯 VALID PRIVATE KEY FOUND: {name}")
            print(f"Private Key: {candidate}")
            print(f"Public Key: {pubkey}")
            return candidate

    return None

def test_nintondo_combinations():
    """Test various combinations with Nintondo"""
    print("\n🔍 TESTING NINTONDO COMBINATIONS")
    print("=" * 50)

    # Genesis block constants
    genesis_timestamp = 1383509530
    genesis_nonce = 44481
    script_constant = 486604799

    # Dogecoin launch date
    dogecoin_date = "20131206"

    combinations = [
        f"Nintondo{genesis_timestamp}",
        f"Nintondo{genesis_nonce}",
        f"Nintondo{script_constant}",
        f"Nintondo{dogecoin_date}",
        f"NintondoDogecoin",
        f"DogecoinNintondo",
        f"Nintondo2013",
        f"Nintendo{genesis_timestamp}",  # Try correct spelling too
        f"Nintendo{dogecoin_date}",
        f"NintendoDogecoin",
    ]

    for combo in combinations:
        candidate = sha256_hash(combo)
        if test_private_key_validity(candidate):
            pubkey = get_public_key_from_private(candidate)
            print(f"🎯 VALID PRIVATE KEY FOUND: {combo}")
            print(f"Private Key: {candidate}")
            print(f"Public Key: {pubkey}")
            return candidate

    return None

def search_for_hidden_constants():
    """Search for other hidden constants that might be private keys"""
    print("\n🔍 SEARCHING FOR HIDDEN CONSTANTS")
    print("=" * 50)

    # Look for other interesting constants in the codebase
    interesting_constants = [
        "9b7bce58999062b63bfb18586813c42491fa32f4591d8d3043cb4fa9e551541b",  # hashGenesisBlock
        "6f80efd038566e1e3eab3e1d38131604d06481e77f2462235c6a9a94b1f8abf9",  # hashMerkleRoot
        "5b2a3f53f605d62c53e62932dac6925e3d74afa5a4b459745c36d42d0ed26a69",  # Expected merkle root
        "caeb449903dc4f0e0ee2",  # PoW value
        "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698",  # Checkpoint hash
    ]

    for constant in interesting_constants:
        if len(constant) == 64 and test_private_key_validity(constant):
            pubkey = get_public_key_from_private(constant)
            print(f"🎯 VALID PRIVATE KEY FOUND: {constant}")
            print(f"Public Key: {pubkey}")
            return constant

        # Also test hash of the constant
        constant_hash = sha256_hash(constant)
        if test_private_key_validity(constant_hash):
            pubkey = get_public_key_from_private(constant_hash)
            print(f"🎯 VALID PRIVATE KEY FOUND (hash): {constant}")
            print(f"Private Key: {constant_hash}")
            print(f"Public Key: {pubkey}")
            return constant_hash

    return None

def analyze_discovered_key(private_key_hex: str):
    """Analyze a discovered private key in detail"""
    print(f"\n🎯 TREASURE ANALYSIS")
    print("=" * 50)

    pubkey = get_public_key_from_private(private_key_hex)

    print(f"Private Key: {private_key_hex}")
    print(f"Public Key: {pubkey}")

    # Check where this key came from in the codebase
    print(f"\nKey source analysis:")
    if private_key_hex == "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698":
        print("🔍 This is the checkpoint hash from checkpoints.cpp!")
        print("🔍 Found at line 30: uint256(\"0xe5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698\")")
        print("🔍 This could be the hidden private key that unlocked Dogecoin's launchpad!")

    # Generate Bitcoin address from this key
    try:
        import hashlib
        import base58

        # Get public key bytes
        pubkey_bytes = bytes.fromhex(pubkey[2:])  # Remove '04' prefix

        # SHA256 then RIPEMD160
        sha256_hash = hashlib.sha256(pubkey_bytes).digest()
        ripemd160_hash = hashlib.new('ripemd160', sha256_hash).digest()

        # Add version byte (0x00 for mainnet)
        versioned_hash = b'\x00' + ripemd160_hash

        # Double SHA256 for checksum
        checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]

        # Combine and encode
        address_bytes = versioned_hash + checksum
        bitcoin_address = base58.b58encode(address_bytes).decode()

        print(f"Bitcoin Address: {bitcoin_address}")

    except Exception as e:
        print(f"Could not generate Bitcoin address: {e}")

    return private_key_hex

def main():
    """Main treasure hunting execution"""
    print("🏴‍☠️ REAL BELLSCOIN TREASURE HUNT")
    print("Searching for the ACTUAL hidden private key...")
    print("(Ignoring the standard Litecoin network parameter)")

    # Phase 1: Scan for unusual hex strings
    hex_candidates = scan_for_hex_strings()
    print(f"Found {len(hex_candidates)} hex string candidates")

    for hex_str in hex_candidates:
        if len(hex_str) == 64 and test_private_key_validity(hex_str):
            pubkey = get_public_key_from_private(hex_str)
            print(f"🎯 VALID PRIVATE KEY FOUND: {hex_str}")
            print(f"Public Key: {pubkey}")
            return analyze_discovered_key(hex_str)

    # Phase 2: Analyze comment hex
    result = analyze_comment_hex()
    if result:
        return analyze_discovered_key(result)

    # Phase 3: Test Nintondo combinations
    result = test_nintondo_combinations()
    if result:
        return analyze_discovered_key(result)

    # Phase 4: Search hidden constants
    result = search_for_hidden_constants()
    if result:
        return analyze_discovered_key(result)

    print("\n💔 No hidden private key found yet")
    print("🔍 The treasure might be hidden in a more complex pattern...")
    print("🔍 Consider looking at steganographic patterns or encoded data...")

    return None

if __name__ == "__main__":
    main()
