Copyright (c) 2009-2012 Bitcoin Developers
Copyright (c) 2011-2012 Litecoin Developers
Distributed under the MIT/X11 software license, see the accompanying
file COPYING or http://www.opensource.org/licenses/mit-license.php.
This product includes software developed by the OpenSSL Project for use in
the OpenSSL Toolkit (http://www.openssl.org/).  This product includes
cryptographic software written by <PERSON> (<EMAIL>) and UPnP
software written by <PERSON>.


Mac OS X litecoind build instructions
<PERSON><PERSON><PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>


See readme-qt.rst for instructions on building Litecoin-QT, the
graphical user interface.

Tested on 10.5, 10.6 and 10.7 intel.  PPC is not supported because it's big-endian.

All of the commands should be executed in Terminal.app.. it's in
/Applications/Utilities

You need to install XCode with all the options checked so that the compiler and
everything is available in /usr not just /Developer. XCode should be available on your OS X
install DVD, but if not, you can get the current version from https://developer.apple.com/xcode/


1.  Clone the github tree to get the source code:

<NAME_EMAIL>:litecoin-project/litecoin.git litecoin

2.  Download and install MacPorts from http://www.macports.org/

2a. (for 10.7 Lion)
    Edit /opt/local/etc/macports/macports.conf and uncomment "build_arch i386"

3.  Install dependencies from MacPorts

sudo port install boost db48 openssl miniupnpc

Optionally install qrencode (and set USE_QRCODE=1):
sudo port install qrencode

4.  Now you should be able to build litecoind:

cd litecoin/src
make -f makefile.osx USE_IPV6=1

Run:
  ./litecoind --help  # for a list of command-line options.
Run
  ./litecoind -daemon # to start the litecoin daemon.
Run
  ./litecoind help # When the daemon is running, to get a list of RPC commands
