<?xml version="1.0" ?><!DOCTYPE TS><TS language="zh_TW" version="2.0">
<defaultcodec>UTF-8</defaultcodec>
<context>
    <name>AboutDialog</name>
    <message>
        <location filename="../forms/aboutdialog.ui" line="14"/>
        <source>About Bitcoin</source>
        <translation>關於位元幣</translation>
    </message>
    <message>
        <location filename="../forms/aboutdialog.ui" line="53"/>
        <source>&lt;b&gt;Bitcoin&lt;/b&gt; version</source>
        <translation>&lt;b&gt;位元幣&lt;/b&gt;版本</translation>
    </message>
    <message>
        <location filename="../forms/aboutdialog.ui" line="97"/>
        <source>Copyright © 2009-2012 Bitcoin Developers

This is experimental software.

Distributed under the MIT/X11 software license, see the accompanying file license.txt or http://www.opensource.org/licenses/mit-license.php.

This product includes software developed by the OpenSSL Project for use in the OpenSSL Toolkit (http://www.openssl.org/) and cryptographic software written by <PERSON> (<EMAIL>) and UPnP software written by <PERSON>.</source>
        <translation>版權為 Bitcoin 開發人員自西元 2009 至 2012 年起所有

這是個實驗性的軟體.

此軟體依據 MIX/X11 軟體授權條款散布, 詳情請見附帶的 license.txt 檔案, 或是以下網站: http://www.opensource.org/licenses/mit-license.php.

此產品也包含了由 OpenSSL Project 所開發的 OpenSSL Toolkit (http://www.openssl.org/) 軟體, 由 Eric Young (<EMAIL>) 撰寫的加解密軟體, 以及由 Thomas Bernard 所撰寫的 UPnP 軟體.</translation>
    </message>
</context>
<context>
    <name>AddressBookPage</name>
    <message>
        <location filename="../forms/addressbookpage.ui" line="14"/>
        <source>Address Book</source>
        <translation>位址簿</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="20"/>
        <source>These are your Bitcoin addresses for receiving payments.  You may want to give a different one to each sender so you can keep track of who is paying you.</source>
        <translation>這是你用來收款的位元幣位址. 你可以提供不同的位址給不同的付款人, 來追蹤是誰支付給你.</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="36"/>
        <source>Double-click to edit address or label</source>
        <translation>點兩下來修改位址或標記</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="63"/>
        <source>Create a new address</source>
        <translation>產生新位址</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="77"/>
        <source>Copy the currently selected address to the system clipboard</source>
        <translation>複製目前選取的位址到系統剪貼簿</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="66"/>
        <source>&amp;New Address</source>
        <translation>新增位址</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="80"/>
        <source>&amp;Copy Address</source>
        <translation>複製位址</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="91"/>
        <source>Show &amp;QR Code</source>
        <translation>顯示 &amp;QR 條碼</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="102"/>
        <source>Sign a message to prove you own this address</source>
        <translation>簽署一則訊息來證明你擁有這個位址</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="105"/>
        <source>&amp;Sign Message</source>
        <translation>簽署訊息</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="116"/>
        <source>Delete the currently selected address from the list. Only sending addresses can be deleted.</source>
        <translation>從列表中刪除目前選取的位址. 只能夠刪除付款位址.</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="119"/>
        <source>&amp;Delete</source>
        <translation>刪除</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="63"/>
        <source>Copy &amp;Label</source>
        <translation>複製標記</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="65"/>
        <source>&amp;Edit</source>
        <translation>編輯</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="292"/>
        <source>Export Address Book Data</source>
        <translation>匯出位址簿資料</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="293"/>
        <source>Comma separated file (*.csv)</source>
        <translation>逗號區隔資料檔 (*.csv)</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="306"/>
        <source>Error exporting</source>
        <translation>資料匯出有誤</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="306"/>
        <source>Could not write to file %1.</source>
        <translation>無法寫入檔案 %1.</translation>
    </message>
</context>
<context>
    <name>AddressTableModel</name>
    <message>
        <location filename="../addresstablemodel.cpp" line="142"/>
        <source>Label</source>
        <translation>標記</translation>
    </message>
    <message>
        <location filename="../addresstablemodel.cpp" line="142"/>
        <source>Address</source>
        <translation>位址</translation>
    </message>
    <message>
        <location filename="../addresstablemodel.cpp" line="178"/>
        <source>(no label)</source>
        <translation>(沒有標記)</translation>
    </message>
</context>
<context>
    <name>AskPassphraseDialog</name>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="26"/>
        <source>Passphrase Dialog</source>
        <translation>密碼對話視窗</translation>
    </message>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="47"/>
        <source>Enter passphrase</source>
        <translation>輸入密碼</translation>
    </message>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="61"/>
        <source>New passphrase</source>
        <translation>新的密碼</translation>
    </message>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="75"/>
        <source>Repeat new passphrase</source>
        <translation>重複新密碼</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="33"/>
        <source>Enter the new passphrase to the wallet.&lt;br/&gt;Please use a passphrase of &lt;b&gt;10 or more random characters&lt;/b&gt;, or &lt;b&gt;eight or more words&lt;/b&gt;.</source>
        <translation>輸入錢包的新密碼.&lt;br/&gt;請用&lt;b&gt;10個以上的字元&lt;/b&gt;, 或是&lt;b&gt;8個以上的字詞&lt;/b&gt;.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="34"/>
        <source>Encrypt wallet</source>
        <translation>錢包加密</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="37"/>
        <source>This operation needs your wallet passphrase to unlock the wallet.</source>
        <translation>這個動作需要用你的錢包密碼來解鎖</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="42"/>
        <source>Unlock wallet</source>
        <translation>錢包解鎖</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="45"/>
        <source>This operation needs your wallet passphrase to decrypt the wallet.</source>
        <translation>這個動作需要用你的錢包密碼來解密</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="50"/>
        <source>Decrypt wallet</source>
        <translation>錢包解密</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="53"/>
        <source>Change passphrase</source>
        <translation>變更密碼</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="54"/>
        <source>Enter the old and new passphrase to the wallet.</source>
        <translation>輸入錢包的新舊密碼.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="100"/>
        <source>Confirm wallet encryption</source>
        <translation>錢包加密確認</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="101"/>
        <source>WARNING: If you encrypt your wallet and lose your passphrase, you will &lt;b&gt;LOSE ALL OF YOUR BITCOINS&lt;/b&gt;!
Are you sure you wish to encrypt your wallet?</source>
        <translation>警告: 如果將錢包加密後忘記密碼, 你會&lt;b&gt;失去其中所有的位元幣&lt;/b&gt;!
你確定要將錢包加密嗎?</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="110"/>
        <location filename="../askpassphrasedialog.cpp" line="159"/>
        <source>Wallet encrypted</source>
        <translation>錢包已加密</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="111"/>
        <source>Bitcoin will close now to finish the encryption process. Remember that encrypting your wallet cannot fully protect your bitcoins from being stolen by malware infecting your computer.</source>
        <translation>位元幣現在要關閉以完成加密程序. 請記住, 加密錢包無法完全防止入侵電腦的惡意程式偷取你的位元幣.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="207"/>
        <location filename="../askpassphrasedialog.cpp" line="231"/>
        <source>Warning: The Caps Lock key is on.</source>
        <translation>警告: 鍵盤輸入鎖定為大寫字母中.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="116"/>
        <location filename="../askpassphrasedialog.cpp" line="123"/>
        <location filename="../askpassphrasedialog.cpp" line="165"/>
        <location filename="../askpassphrasedialog.cpp" line="171"/>
        <source>Wallet encryption failed</source>
        <translation>錢包加密失敗</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="117"/>
        <source>Wallet encryption failed due to an internal error. Your wallet was not encrypted.</source>
        <translation>錢包加密因程式內部有誤而失敗. 你的錢包還是沒有加密.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="124"/>
        <location filename="../askpassphrasedialog.cpp" line="172"/>
        <source>The supplied passphrases do not match.</source>
        <translation>提供的密碼不符.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="135"/>
        <source>Wallet unlock failed</source>
        <translation>錢包解鎖失敗</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="136"/>
        <location filename="../askpassphrasedialog.cpp" line="147"/>
        <location filename="../askpassphrasedialog.cpp" line="166"/>
        <source>The passphrase entered for the wallet decryption was incorrect.</source>
        <translation>用來解密錢包的密碼輸入錯誤.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="146"/>
        <source>Wallet decryption failed</source>
        <translation>錢包解密失敗</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="160"/>
        <source>Wallet passphrase was succesfully changed.</source>
        <translation>錢包密碼變更成功.</translation>
    </message>
</context>
<context>
    <name>BitcoinGUI</name>
    <message>
        <location filename="../bitcoingui.cpp" line="73"/>
        <source>Bitcoin Wallet</source>
        <translation>位元幣錢包</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="215"/>
        <source>Sign &amp;message...</source>
        <translation>訊息簽署...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="248"/>
        <source>Show/Hide &amp;Bitcoin</source>
        <translation>顯示/隱藏位元幣</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="515"/>
        <source>Synchronizing with network...</source>
        <translation>網路同步中...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="185"/>
        <source>&amp;Overview</source>
        <translation>總覽</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="186"/>
        <source>Show general overview of wallet</source>
        <translation>顯示錢包一般總覽</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="191"/>
        <source>&amp;Transactions</source>
        <translation>交易</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="192"/>
        <source>Browse transaction history</source>
        <translation>瀏覽交易紀錄</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="197"/>
        <source>&amp;Address Book</source>
        <translation>位址簿</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="198"/>
        <source>Edit the list of stored addresses and labels</source>
        <translation>編輯儲存位址與標記的列表</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="203"/>
        <source>&amp;Receive coins</source>
        <translation>收錢</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="204"/>
        <source>Show the list of addresses for receiving payments</source>
        <translation>顯示收款位址的列表</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="209"/>
        <source>&amp;Send coins</source>
        <translation>付錢</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="216"/>
        <source>Prove you control an address</source>
        <translation>證明你控制一個位址</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="235"/>
        <source>E&amp;xit</source>
        <translation>結束</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="236"/>
        <source>Quit application</source>
        <translation>結束應用程式</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="239"/>
        <source>&amp;About %1</source>
        <translation>關於%1</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="240"/>
        <source>Show information about Bitcoin</source>
        <translation>顯示位元幣相關資訊</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="242"/>
        <source>About &amp;Qt</source>
        <translation>關於 &amp;Qt</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="243"/>
        <source>Show information about Qt</source>
        <translation>顯示有關於 Qt 的資訊</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="245"/>
        <source>&amp;Options...</source>
        <translation>選項...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="252"/>
        <source>&amp;Encrypt Wallet...</source>
        <translation>錢包加密...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="255"/>
        <source>&amp;Backup Wallet...</source>
        <translation>錢包備份...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="257"/>
        <source>&amp;Change Passphrase...</source>
        <translation>密碼變更...</translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="517"/>
        <source>~%n block(s) remaining</source>
        <translation><numerusform>剩下 ~%n 個區塊</numerusform></translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="528"/>
        <source>Downloaded %1 of %2 blocks of transaction history (%3% done).</source>
        <translation>已下載了全部 %2 個中的 %1 個交易紀錄區塊 (已完成 %3%).</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="250"/>
        <source>&amp;Export...</source>
        <translation>匯出...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="210"/>
        <source>Send coins to a Bitcoin address</source>
        <translation>付錢到一個位元幣位址</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="246"/>
        <source>Modify configuration options for Bitcoin</source>
        <translation>修改位元幣的設定選項</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="249"/>
        <source>Show or hide the Bitcoin window</source>
        <translation>顯示或隱藏位元幣的視窗</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="251"/>
        <source>Export the data in the current tab to a file</source>
        <translation>將目前分頁的資料匯出存成檔案</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="253"/>
        <source>Encrypt or decrypt wallet</source>
        <translation>將錢包加解密</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="256"/>
        <source>Backup wallet to another location</source>
        <translation>將錢包備份到其它地方</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="258"/>
        <source>Change the passphrase used for wallet encryption</source>
        <translation>變更錢包加密用的密碼</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="259"/>
        <source>&amp;Debug window</source>
        <translation>除錯視窗</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="260"/>
        <source>Open debugging and diagnostic console</source>
        <translation>開啓除錯與診斷主控台</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="261"/>
        <source>&amp;Verify message...</source>
        <translation>訊息驗證...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="262"/>
        <source>Verify a message signature</source>
        <translation>驗證訊息簽章</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="286"/>
        <source>&amp;File</source>
        <translation>檔案</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="296"/>
        <source>&amp;Settings</source>
        <translation>設定</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="302"/>
        <source>&amp;Help</source>
        <translation>求助</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="311"/>
        <source>Tabs toolbar</source>
        <translation>分頁工具列</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="322"/>
        <source>Actions toolbar</source>
        <translation>動作工具列</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="334"/>
        <location filename="../bitcoingui.cpp" line="343"/>
        <source>[testnet]</source>
        <translation>[testnet]</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="343"/>
        <location filename="../bitcoingui.cpp" line="399"/>
        <source>Bitcoin client</source>
        <translation>位元幣客戶軟體</translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="492"/>
        <source>%n active connection(s) to Bitcoin network</source>
        <translation><numerusform>與位元幣網路有 %n 個連線在使用中</numerusform></translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="540"/>
        <source>Downloaded %1 blocks of transaction history.</source>
        <translation>已下載了 %1 個交易紀錄的區塊.</translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="555"/>
        <source>%n second(s) ago</source>
        <translation><numerusform>%n 秒鐘前</numerusform></translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="559"/>
        <source>%n minute(s) ago</source>
        <translation><numerusform>%n 分鐘前</numerusform></translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="563"/>
        <source>%n hour(s) ago</source>
        <translation><numerusform>%n 小時前</numerusform></translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="567"/>
        <source>%n day(s) ago</source>
        <translation><numerusform>%n 天前</numerusform></translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="573"/>
        <source>Up to date</source>
        <translation>最新狀態</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="580"/>
        <source>Catching up...</source>
        <translation>進度追趕中...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="590"/>
        <source>Last received block was generated %1.</source>
        <translation>最近收到的區塊產生於 %1.</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="649"/>
        <source>This transaction is over the size limit.  You can still send it for a fee of %1, which goes to the nodes that process your transaction and helps to support the network.  Do you want to pay the fee?</source>
        <translation>這筆交易的資料大小超過限制了. 你還是可以付出 %1 的費用來傳送. 這筆費用會付給處理該筆交易的節點, 並幫助維持整個網路. 你願意支付這項費用嗎?</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="654"/>
        <source>Confirm transaction fee</source>
        <translation>確認交易手續費</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="681"/>
        <source>Sent transaction</source>
        <translation>付款交易</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="682"/>
        <source>Incoming transaction</source>
        <translation>收款交易</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="683"/>
        <source>Date: %1
Amount: %2
Type: %3
Address: %4
</source>
        <translation>日期: %1
金額: %2
類別: %3
位址: %4</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="804"/>
        <source>Wallet is &lt;b&gt;encrypted&lt;/b&gt; and currently &lt;b&gt;unlocked&lt;/b&gt;</source>
        <translation>錢包&lt;b&gt;已加密&lt;/b&gt;並且正&lt;b&gt;解鎖中&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="812"/>
        <source>Wallet is &lt;b&gt;encrypted&lt;/b&gt; and currently &lt;b&gt;locked&lt;/b&gt;</source>
        <translation>錢包&lt;b&gt;已加密&lt;/b&gt;並且正&lt;b&gt;上鎖中&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="835"/>
        <source>Backup Wallet</source>
        <translation>錢包備份</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="835"/>
        <source>Wallet Data (*.dat)</source>
        <translation>錢包資料檔 (*.dat)</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="838"/>
        <source>Backup Failed</source>
        <translation>備份失敗</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="838"/>
        <source>There was an error trying to save the wallet data to the new location.</source>
        <translation>儲存錢包資料到新的地方時發生錯誤</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="112"/>
        <source>A fatal error occured. Bitcoin can no longer continue safely and will quit.</source>
        <translation>發生了致命的錯誤. 位元幣程式將無法繼續安全執行, 只好結束.</translation>
    </message>
</context>
<context>
    <name>ClientModel</name>
    <message>
        <location filename="../clientmodel.cpp" line="84"/>
        <source>Network Alert</source>
        <translation>網路警報</translation>
    </message>
</context>
<context>
    <name>DisplayOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="246"/>
        <source>Display</source>
        <translation>顯示</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="257"/>
        <source>default</source>
        <translation>預設</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="263"/>
        <source>The user interface language can be set here. This setting will only take effect after restarting Bitcoin.</source>
        <translation>可以在這裡設定使用者介面的語言. 這個設定在位元幣程式重啓後才會生效.</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="252"/>
        <source>User Interface &amp;Language:</source>
        <translation>使用界面語言</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="273"/>
        <source>&amp;Unit to show amounts in:</source>
        <translation>金額顯示單位:</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="277"/>
        <source>Choose the default subdivision unit to show in the interface, and when sending coins</source>
        <translation>選擇操作界面與付錢時預設顯示的細分單位</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="284"/>
        <source>&amp;Display addresses in transaction list</source>
        <translation>在交易列表顯示位址</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="285"/>
        <source>Whether to show Bitcoin addresses in the transaction list</source>
        <translation>是否要在交易列表中顯示位元幣位址</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="303"/>
        <source>Warning</source>
        <translation>警告</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="303"/>
        <source>This setting will take effect after restarting Bitcoin.</source>
        <translation>這個設定會在位元幣程式重啓後生效.</translation>
    </message>
</context>
<context>
    <name>EditAddressDialog</name>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="14"/>
        <source>Edit Address</source>
        <translation>編輯位址</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="25"/>
        <source>&amp;Label</source>
        <translation>標記</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="35"/>
        <source>The label associated with this address book entry</source>
        <translation>與這個位址簿項目關聯的標記</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="42"/>
        <source>&amp;Address</source>
        <translation>位址</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="52"/>
        <source>The address associated with this address book entry. This can only be modified for sending addresses.</source>
        <translation>與這個位址簿項目關聯的位址. 只能修改付款位址.</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="20"/>
        <source>New receiving address</source>
        <translation>新收款位址</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="24"/>
        <source>New sending address</source>
        <translation>新付款位址</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="27"/>
        <source>Edit receiving address</source>
        <translation>編輯收款位址</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="31"/>
        <source>Edit sending address</source>
        <translation>編輯付款位址</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="91"/>
        <source>The entered address &quot;%1&quot; is already in the address book.</source>
        <translation>輸入的位址&quot;%1&quot;已存在於位址簿中.</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="96"/>
        <source>The entered address &quot;%1&quot; is not a valid Bitcoin address.</source>
        <translation>輸入的位址 &quot;%1&quot; 並不是有效的位元幣位址.</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="101"/>
        <source>Could not unlock wallet.</source>
        <translation>無法將錢包解鎖.</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="106"/>
        <source>New key generation failed.</source>
        <translation>新密鑰產生失敗.</translation>
    </message>
</context>
<context>
    <name>HelpMessageBox</name>
    <message>
        <location filename="../bitcoin.cpp" line="133"/>
        <location filename="../bitcoin.cpp" line="143"/>
        <source>Bitcoin-Qt</source>
        <translation>位元幣-Qt</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="133"/>
        <source>version</source>
        <translation>版本</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="135"/>
        <source>Usage:</source>
        <translation>用法:</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="136"/>
        <source>options</source>
        <translation>選項</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="138"/>
        <source>UI options</source>
        <translation>使用界面選項</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="139"/>
        <source>Set language, for example &quot;de_DE&quot; (default: system locale)</source>
        <translation>設定語言, 比如說 &quot;de_DE&quot; (預設: 系統語系)</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="140"/>
        <source>Start minimized</source>
        <translation>啓動時最小化
</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="141"/>
        <source>Show splash screen on startup (default: 1)</source>
        <translation>顯示啓動畫面 (預設: 1)</translation>
    </message>
</context>
<context>
    <name>MainOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="227"/>
        <source>Detach block and address databases at shutdown. This means they can be moved to another data directory, but it slows down shutdown. The wallet is always detached.</source>
        <translation>關掉程式時卸載區塊與位址的資料庫. 表示說資料庫會被搬到別的資料目錄去, 且會造成程式關掉的比較慢. 錢包則總是會被卸載.</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="212"/>
        <source>Pay transaction &amp;fee</source>
        <translation>付交易手續費</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="204"/>
        <source>Main</source>
        <translation>主要</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="206"/>
        <source>Optional transaction fee per kB that helps make sure your transactions are processed quickly. Most transactions are 1 kB. Fee 0.01 recommended.</source>
        <translation>非必要的交易手續費, 以 kB 為計費單位, 且有助於縮短你的交易處理時間. 大部份交易的資料大小是 1 kB. 建議設定為 0.01 元.</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="222"/>
        <source>&amp;Start Bitcoin on system login</source>
        <translation>系統登入時啟動位元幣</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="223"/>
        <source>Automatically start Bitcoin after logging in to the system</source>
        <translation>在登入系統後自動啓動位元幣</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="226"/>
        <source>&amp;Detach databases at shutdown</source>
        <translation>關閉時卸載資料庫</translation>
    </message>
</context>
<context>
    <name>MessagePage</name>
    <message>
        <location filename="../forms/messagepage.ui" line="14"/>
        <source>Sign Message</source>
        <translation>訊息簽署</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="20"/>
        <source>You can sign messages with your addresses to prove you own them. Be careful not to sign anything vague, as phishing attacks may try to trick you into signing your identity over to them. Only sign fully-detailed statements you agree to.</source>
        <translation>你可以用你的位址來簽署訊息, 以證明你對它的所有權. 但是請小心, 不要簽署語意含糊不清的內容, 因為釣魚式詐騙可能會用騙你簽署的手法來冒充是你. 只有在語句中的細節你都同意時才簽署.</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="38"/>
        <source>The address to sign the message with  (e.g. **********************************)</source>
        <translation>用來簽署訊息的位址 (比如說 **********************************)</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="48"/>
        <source>Choose adress from address book</source>
        <translation>從位址簿中選一個位址</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="58"/>
        <source>Alt+A</source>
        <translation>Alt+A</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="71"/>
        <source>Paste address from clipboard</source>
        <translation>從剪貼簿貼上位址</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="81"/>
        <source>Alt+P</source>
        <translation>Alt+P</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="93"/>
        <source>Enter the message you want to sign here</source>
        <translation>在這裡輸入你想簽署的訊息</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="128"/>
        <source>Copy the current signature to the system clipboard</source>
        <translation>複製目前的簽章到系統剪貼簿</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="131"/>
        <source>&amp;Copy Signature</source>
        <translation>複製簽章</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="142"/>
        <source>Reset all sign message fields</source>
        <translation>重置所有訊息簽署欄位</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="145"/>
        <source>Clear &amp;All</source>
        <translation>全部清掉</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="31"/>
        <source>Click &quot;Sign Message&quot; to get signature</source>
        <translation>按&quot;簽署訊息&quot;來取得簽章</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="114"/>
        <source>Sign a message to prove you own this address</source>
        <translation>簽署一則訊息來證明你擁有這個位址</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="117"/>
        <source>&amp;Sign Message</source>
        <translation>簽署訊息</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="30"/>
        <source>Enter a Bitcoin address (e.g. **********************************)</source>
        <translation>輸入位元幣位址 (比如說 **********************************)</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="83"/>
        <location filename="../messagepage.cpp" line="90"/>
        <location filename="../messagepage.cpp" line="105"/>
        <location filename="../messagepage.cpp" line="117"/>
        <source>Error signing</source>
        <translation>簽署發生錯誤</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="83"/>
        <source>%1 is not a valid address.</source>
        <translation>%1 不是個有效的位址.</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="90"/>
        <source>%1 does not refer to a key.</source>
        <translation>%1 沒有指到任何密鑰.</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="105"/>
        <source>Private key for %1 is not available.</source>
        <translation>沒有 %1 的密鑰.</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="117"/>
        <source>Sign failed</source>
        <translation>簽署失敗</translation>
    </message>
</context>
<context>
    <name>NetworkOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="345"/>
        <source>Network</source>
        <translation>網路</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="347"/>
        <source>Map port using &amp;UPnP</source>
        <translation>用 &amp;UPnP 設定通訊埠對應</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="348"/>
        <source>Automatically open the Bitcoin client port on the router. This only works when your router supports UPnP and it is enabled.</source>
        <translation>自動在路由器上開啟 Bitcoin 的客戶端通訊埠. 只有在你的路由器支援 UPnP 且開啟時才有作用.</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="351"/>
        <source>&amp;Connect through SOCKS4 proxy:</source>
        <translation>透過 SOCKS4 代理伺服器連線:</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="352"/>
        <source>Connect to the Bitcon network through a SOCKS4 proxy (e.g. when connecting through Tor)</source>
        <translation>透過 SOCKS4 代理伺服器連線至位元幣網路 (比如說透過 Tor)</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="357"/>
        <source>Proxy &amp;IP:</source>
        <translation>代理伺服器位址:</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="366"/>
        <source>&amp;Port:</source>
        <translation>通訊埠:</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="363"/>
        <source>IP address of the proxy (e.g. 127.0.0.1)</source>
        <translation>代理伺服器的網際網路位址 (比如說 127.0.0.1)</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="372"/>
        <source>Port of the proxy (e.g. 1234)</source>
        <translation>代理伺服器的通訊埠 (比如說 1234)</translation>
    </message>
</context>
<context>
    <name>OptionsDialog</name>
    <message>
        <location filename="../optionsdialog.cpp" line="135"/>
        <source>Options</source>
        <translation>選項</translation>
    </message>
</context>
<context>
    <name>OverviewPage</name>
    <message>
        <location filename="../forms/overviewpage.ui" line="14"/>
        <source>Form</source>
        <translation>表單</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="47"/>
        <location filename="../forms/overviewpage.ui" line="204"/>
        <source>The displayed information may be out of date. Your wallet automatically synchronizes with the Bitcoin network after a connection is established, but this process has not completed yet.</source>
        <translation>顯示的資訊可能是過期的. 與位元幣網路的連線建立後, 你的錢包會自動和網路同步, 但這個步驟還沒完成.</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="89"/>
        <source>Balance:</source>
        <translation>餘額:</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="147"/>
        <source>Number of transactions:</source>
        <translation>交易次數:</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="118"/>
        <source>Unconfirmed:</source>
        <translation>未確認額:</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="40"/>
        <source>Wallet</source>
        <translation>錢包</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="197"/>
        <source>&lt;b&gt;Recent transactions&lt;/b&gt;</source>
        <translation>&lt;b&gt;最近交易&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="105"/>
        <source>Your current balance</source>
        <translation>目前餘額</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="134"/>
        <source>Total of transactions that have yet to be confirmed, and do not yet count toward the current balance</source>
        <translation>尚未確認之交易的總額, 不包含在目前餘額中</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="154"/>
        <source>Total number of transactions in wallet</source>
        <translation>錢包中紀錄的總交易次數</translation>
    </message>
    <message>
        <location filename="../overviewpage.cpp" line="110"/>
        <location filename="../overviewpage.cpp" line="111"/>
        <source>out of sync</source>
        <translation>沒同步</translation>
    </message>
</context>
<context>
    <name>QRCodeDialog</name>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="14"/>
        <source>QR Code Dialog</source>
        <translation>QR 條碼對話視窗</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="32"/>
        <source>QR Code</source>
        <translation>QR 條碼</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="55"/>
        <source>Request Payment</source>
        <translation>付款單</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="70"/>
        <source>Amount:</source>
        <translation>金額:</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="105"/>
        <source>BTC</source>
        <translation>BTC</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="121"/>
        <source>Label:</source>
        <translation>標記:</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="144"/>
        <source>Message:</source>
        <translation>訊息:</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="186"/>
        <source>&amp;Save As...</source>
        <translation>儲存為...</translation>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="45"/>
        <source>Error encoding URI into QR Code.</source>
        <translation>將 URI 編碼成 QR 條碼時發生錯誤</translation>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="63"/>
        <source>Resulting URI too long, try to reduce the text for label / message.</source>
        <translation>造出的網址太長了,請把標籤或訊息的文字縮短再試看看.</translation>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="120"/>
        <source>Save QR Code</source>
        <translation>儲存 QR 條碼</translation>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="120"/>
        <source>PNG Images (*.png)</source>
        <translation>PNG 圖檔 (*.png)</translation>
    </message>
</context>
<context>
    <name>RPCConsole</name>
    <message>
        <location filename="../forms/rpcconsole.ui" line="14"/>
        <source>Bitcoin debug window</source>
        <translation>位元幣除錯視窗</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="46"/>
        <source>Client name</source>
        <translation>客戶端程式名稱</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="56"/>
        <location filename="../forms/rpcconsole.ui" line="79"/>
        <location filename="../forms/rpcconsole.ui" line="102"/>
        <location filename="../forms/rpcconsole.ui" line="125"/>
        <location filename="../forms/rpcconsole.ui" line="161"/>
        <location filename="../forms/rpcconsole.ui" line="214"/>
        <location filename="../forms/rpcconsole.ui" line="237"/>
        <location filename="../forms/rpcconsole.ui" line="260"/>
        <location filename="../rpcconsole.cpp" line="245"/>
        <source>N/A</source>
        <translation>無</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="69"/>
        <source>Client version</source>
        <translation>客戶端程式版本</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="24"/>
        <source>&amp;Information</source>
        <translation>資訊</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="39"/>
        <source>Client</source>
        <translation>用戶端程式</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="115"/>
        <source>Startup time</source>
        <translation>啓動時間</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="144"/>
        <source>Network</source>
        <translation>網路</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="151"/>
        <source>Number of connections</source>
        <translation>連線數</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="174"/>
        <source>On testnet</source>
        <translation>位於測試網路</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="197"/>
        <source>Block chain</source>
        <translation>區塊鎖鏈</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="204"/>
        <source>Current number of blocks</source>
        <translation>目前區塊數</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="227"/>
        <source>Estimated total blocks</source>
        <translation>估計總區塊數</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="250"/>
        <source>Last block time</source>
        <translation>最近區塊時間</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="292"/>
        <source>Debug logfile</source>
        <translation>除錯紀錄檔</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="299"/>
        <source>Open the Bitcoin debug logfile from the current data directory. This can take a few seconds for large logfiles.</source>
        <translation>從目前的資料目錄下開啓位元幣的除錯紀錄檔. 當紀錄檔很大時可能要花好幾秒的時間.</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="302"/>
        <source>&amp;Open</source>
        <translation>開啓</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="323"/>
        <source>&amp;Console</source>
        <translation>主控台</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="92"/>
        <source>Build date</source>
        <translation>建置日期</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="372"/>
        <source>Clear console</source>
        <translation>清主控台</translation>
    </message>
    <message>
        <location filename="../rpcconsole.cpp" line="212"/>
        <source>Welcome to the Bitcoin RPC console.</source>
        <translation>歡迎使用位元幣 RPC 主控台.</translation>
    </message>
    <message>
        <location filename="../rpcconsole.cpp" line="213"/>
        <source>Use up and down arrows to navigate history, and &lt;b&gt;Ctrl-L&lt;/b&gt; to clear screen.</source>
        <translation>請用上下游標鍵來瀏覽歷史指令, 且可用 &lt;b&gt;Ctrl-L&lt;/b&gt; 來清理畫面.</translation>
    </message>
    <message>
        <location filename="../rpcconsole.cpp" line="214"/>
        <source>Type &lt;b&gt;help&lt;/b&gt; for an overview of available commands.</source>
        <translation>請打 &lt;b&gt;help&lt;/b&gt; 來看可用指令的簡介.</translation>
    </message>
</context>
<context>
    <name>SendCoinsDialog</name>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="14"/>
        <location filename="../sendcoinsdialog.cpp" line="122"/>
        <location filename="../sendcoinsdialog.cpp" line="127"/>
        <location filename="../sendcoinsdialog.cpp" line="132"/>
        <location filename="../sendcoinsdialog.cpp" line="137"/>
        <location filename="../sendcoinsdialog.cpp" line="143"/>
        <location filename="../sendcoinsdialog.cpp" line="148"/>
        <location filename="../sendcoinsdialog.cpp" line="153"/>
        <source>Send Coins</source>
        <translation>付錢</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="64"/>
        <source>Send to multiple recipients at once</source>
        <translation>一次付給多個人</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="67"/>
        <source>&amp;Add Recipient</source>
        <translation>加收款人</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="84"/>
        <source>Remove all transaction fields</source>
        <translation>移除所有交易欄位</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="87"/>
        <source>Clear &amp;All</source>
        <translation>全部清掉</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="106"/>
        <source>Balance:</source>
        <translation>餘額:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="113"/>
        <source>123.456 BTC</source>
        <translation>123.456 BTC</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="144"/>
        <source>Confirm the send action</source>
        <translation>確認付款動作</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="147"/>
        <source>&amp;Send</source>
        <translation>付出</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="94"/>
        <source>&lt;b&gt;%1&lt;/b&gt; to %2 (%3)</source>
        <translation>&lt;b&gt;%1&lt;/b&gt; 給 %2 (%3)</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="99"/>
        <source>Confirm send coins</source>
        <translation>確認付出金額</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="100"/>
        <source>Are you sure you want to send %1?</source>
        <translation>確定要付出 %1 嗎?</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="100"/>
        <source> and </source>
        <translation>和</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="123"/>
        <source>The recepient address is not valid, please recheck.</source>
        <translation>無效的收款位址, 請再檢查看看.</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="128"/>
        <source>The amount to pay must be larger than 0.</source>
        <translation>付款金額必須大於 0.</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="133"/>
        <source>The amount exceeds your balance.</source>
        <translation>金額超過了餘額</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="138"/>
        <source>The total exceeds your balance when the %1 transaction fee is included.</source>
        <translation>包含 %1 的交易手續費後, 總金額超過了你的餘額</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="144"/>
        <source>Duplicate address found, can only send to each address once per send operation.</source>
        <translation>發現有重複的位址. 在一次付款動作中, 只能付給每個位址一次.</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="149"/>
        <source>Error: Transaction creation failed.</source>
        <translation>錯誤: 交易產生失敗.</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="154"/>
        <source>Error: The transaction was rejected. This might happen if some of the coins in your wallet were already spent, such as if you used a copy of wallet.dat and coins were spent in the copy but not marked as spent here.</source>
        <translation>錯誤: 交易被拒絕. 有時候會發生這種錯誤, 是因為你錢包中的一些錢已經被花掉了. 比如說你複製了錢包檔 wallet.dat, 然後用複製的錢包花掉了錢, 你現在所用的原來的錢包中卻沒有該筆交易紀錄.</translation>
    </message>
</context>
<context>
    <name>SendCoinsEntry</name>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="14"/>
        <source>Form</source>
        <translation>表單</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="29"/>
        <source>A&amp;mount:</source>
        <translation>金額:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="42"/>
        <source>Pay &amp;To:</source>
        <translation>付給:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="66"/>
        <location filename="../sendcoinsentry.cpp" line="25"/>
        <source>Enter a label for this address to add it to your address book</source>
        <translation>給這個位址輸入一個標記, 並加到位址簿中</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="75"/>
        <source>&amp;Label:</source>
        <translation>標記:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="93"/>
        <source>The address to send the payment to  (e.g. **********************************)</source>
        <translation>付款的目標位址 (比如說 **********************************)</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="103"/>
        <source>Choose address from address book</source>
        <translation>從位址簿中選一個位址</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="113"/>
        <source>Alt+A</source>
        <translation>Alt+A</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="120"/>
        <source>Paste address from clipboard</source>
        <translation>從剪貼簿貼上位址</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="130"/>
        <source>Alt+P</source>
        <translation>Alt+P</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="137"/>
        <source>Remove this recipient</source>
        <translation>去掉這個收款人</translation>
    </message>
    <message>
        <location filename="../sendcoinsentry.cpp" line="26"/>
        <source>Enter a Bitcoin address (e.g. **********************************)</source>
        <translation>輸入位元幣位址 (比如說 **********************************)</translation>
    </message>
</context>
<context>
    <name>TransactionDesc</name>
    <message>
        <location filename="../transactiondesc.cpp" line="21"/>
        <source>Open for %1 blocks</source>
        <translation>在 %1 個區塊內未定</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="23"/>
        <source>Open until %1</source>
        <translation>在 %1 前未定</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="29"/>
        <source>%1/offline?</source>
        <translation>%1/離線中?</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="31"/>
        <source>%1/unconfirmed</source>
        <translation>%1/未確認</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="33"/>
        <source>%1 confirmations</source>
        <translation>經確認 %1 次</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="51"/>
        <source>&lt;b&gt;Status:&lt;/b&gt; </source>
        <translation>&lt;b&gt;狀態:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="56"/>
        <source>, has not been successfully broadcast yet</source>
        <translation>, 尚未成功公告出去</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="58"/>
        <source>, broadcast through %1 node</source>
        <translation>, 已公告至 %1 個節點</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="60"/>
        <source>, broadcast through %1 nodes</source>
        <translation>, 已公告至 %1 個節點</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="64"/>
        <source>&lt;b&gt;Date:&lt;/b&gt; </source>
        <translation>&lt;b&gt;日期:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="71"/>
        <source>&lt;b&gt;Source:&lt;/b&gt; Generated&lt;br&gt;</source>
        <translation>&lt;b&gt;來源:&lt;/b&gt; 生產所得&lt;br&gt;</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="77"/>
        <location filename="../transactiondesc.cpp" line="94"/>
        <source>&lt;b&gt;From:&lt;/b&gt; </source>
        <translation>&lt;b&gt;來自:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="94"/>
        <source>unknown</source>
        <translation>未知</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="95"/>
        <location filename="../transactiondesc.cpp" line="118"/>
        <location filename="../transactiondesc.cpp" line="178"/>
        <source>&lt;b&gt;To:&lt;/b&gt; </source>
        <translation>&lt;b&gt;目的:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="98"/>
        <source> (yours, label: </source>
        <translation> (你的, 標記為: </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="100"/>
        <source> (yours)</source>
        <translation> (你的)</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="136"/>
        <location filename="../transactiondesc.cpp" line="150"/>
        <location filename="../transactiondesc.cpp" line="195"/>
        <location filename="../transactiondesc.cpp" line="212"/>
        <source>&lt;b&gt;Credit:&lt;/b&gt; </source>
        <translation>&lt;b&gt;入帳:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="138"/>
        <source>(%1 matures in %2 more blocks)</source>
        <translation>(%1 將在 %2 個區塊產出後熟成)</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="142"/>
        <source>(not accepted)</source>
        <translation>(不被接受)</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="186"/>
        <location filename="../transactiondesc.cpp" line="194"/>
        <location filename="../transactiondesc.cpp" line="209"/>
        <source>&lt;b&gt;Debit:&lt;/b&gt; </source>
        <translation>&lt;b&gt;出帳:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="200"/>
        <source>&lt;b&gt;Transaction fee:&lt;/b&gt; </source>
        <translation>&lt;b&gt;交易手續費:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="216"/>
        <source>&lt;b&gt;Net amount:&lt;/b&gt; </source>
        <translation>&lt;b&gt;淨額:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="222"/>
        <source>Message:</source>
        <translation>訊息:</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="224"/>
        <source>Comment:</source>
        <translation>附註:</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="226"/>
        <source>Transaction ID:</source>
        <translation>交易識別碼:</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="229"/>
        <source>Generated coins must wait 120 blocks before they can be spent.  When you generated this block, it was broadcast to the network to be added to the block chain.  If it fails to get into the chain, it will change to &quot;not accepted&quot; and not be spendable.  This may occasionally happen if another node generates a block within a few seconds of yours.</source>
        <translation>生產出來的錢要再等 120 個區塊產出之後, 才能夠花用. 當你產出區塊時, 它會被公布到網路上, 以被串連至區塊鎖鏈. 如果串連失敗了, 它的狀態就會變成&quot;不被接受&quot;, 且不能被花用. 當你產出區塊的幾秒鐘內, 其他節點也產出了區塊的話, 有時候就會發生這種情形.</translation>
    </message>
</context>
<context>
    <name>TransactionDescDialog</name>
    <message>
        <location filename="../forms/transactiondescdialog.ui" line="14"/>
        <source>Transaction details</source>
        <translation>交易明細</translation>
    </message>
    <message>
        <location filename="../forms/transactiondescdialog.ui" line="20"/>
        <source>This pane shows a detailed description of the transaction</source>
        <translation>此版面顯示交易的詳細說明</translation>
    </message>
</context>
<context>
    <name>TransactionTableModel</name>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Date</source>
        <translation>日期</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Type</source>
        <translation>種類</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Address</source>
        <translation>位址</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Amount</source>
        <translation>金額</translation>
    </message>
    <message numerus="yes">
        <location filename="../transactiontablemodel.cpp" line="281"/>
        <source>Open for %n block(s)</source>
        <translation><numerusform>在 %n 個區塊內未定</numerusform></translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="284"/>
        <source>Open until %1</source>
        <translation>在 %1 前未定</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="287"/>
        <source>Offline (%1 confirmations)</source>
        <translation>離線中 (經確認 %1 次)</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="290"/>
        <source>Unconfirmed (%1 of %2 confirmations)</source>
        <translation>未確認 (經確認 %1 次, 應確認 %2 次)</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="293"/>
        <source>Confirmed (%1 confirmations)</source>
        <translation>已確認 (經確認 %1 次)</translation>
    </message>
    <message numerus="yes">
        <location filename="../transactiontablemodel.cpp" line="301"/>
        <source>Mined balance will be available in %n more blocks</source>
        <translation><numerusform>生產金額將在 %n 個區塊產出後可用</numerusform></translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="307"/>
        <source>This block was not received by any other nodes and will probably not be accepted!</source>
        <translation>沒有其他節點收到這個區塊, 也許它不被接受!</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="310"/>
        <source>Generated but not accepted</source>
        <translation>產出但不被接受</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="353"/>
        <source>Received with</source>
        <translation>收受於</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="355"/>
        <source>Received from</source>
        <translation>收受自</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="358"/>
        <source>Sent to</source>
        <translation>付出至</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="360"/>
        <source>Payment to yourself</source>
        <translation>付給自己</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="362"/>
        <source>Mined</source>
        <translation>開採所得</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="400"/>
        <source>(n/a)</source>
        <translation>(不適用)</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="599"/>
        <source>Transaction status. Hover over this field to show number of confirmations.</source>
        <translation>交易狀態. 移動游標至欄位上方來顯示確認次數.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="601"/>
        <source>Date and time that the transaction was received.</source>
        <translation>收到交易的日期與時間.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="603"/>
        <source>Type of transaction.</source>
        <translation>交易的種類.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="605"/>
        <source>Destination address of transaction.</source>
        <translation>交易的目標位址.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="607"/>
        <source>Amount removed from or added to balance.</source>
        <translation>減去或加入至餘額的金額</translation>
    </message>
</context>
<context>
    <name>TransactionView</name>
    <message>
        <location filename="../transactionview.cpp" line="55"/>
        <location filename="../transactionview.cpp" line="71"/>
        <source>All</source>
        <translation>全部</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="56"/>
        <source>Today</source>
        <translation>今天</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="57"/>
        <source>This week</source>
        <translation>這週</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="58"/>
        <source>This month</source>
        <translation>這個月</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="59"/>
        <source>Last month</source>
        <translation>上個月</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="60"/>
        <source>This year</source>
        <translation>今年</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="61"/>
        <source>Range...</source>
        <translation>指定範圍...</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="72"/>
        <source>Received with</source>
        <translation>收受於</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="74"/>
        <source>Sent to</source>
        <translation>付出至</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="76"/>
        <source>To yourself</source>
        <translation>給自己</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="77"/>
        <source>Mined</source>
        <translation>開採所得</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="78"/>
        <source>Other</source>
        <translation>其他</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="85"/>
        <source>Enter address or label to search</source>
        <translation>輸入位址或標記來搜尋</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="92"/>
        <source>Min amount</source>
        <translation>最小金額</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="126"/>
        <source>Copy address</source>
        <translation>複製位址</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="127"/>
        <source>Copy label</source>
        <translation>複製標記</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="128"/>
        <source>Copy amount</source>
        <translation>複製金額</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="129"/>
        <source>Edit label</source>
        <translation>編輯標記</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="130"/>
        <source>Show transaction details</source>
        <translation>顯示交易明細</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="270"/>
        <source>Export Transaction Data</source>
        <translation>匯出交易資料</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="271"/>
        <source>Comma separated file (*.csv)</source>
        <translation>逗號分隔資料檔 (*.csv)</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="279"/>
        <source>Confirmed</source>
        <translation>已確認</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="280"/>
        <source>Date</source>
        <translation>日期</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="281"/>
        <source>Type</source>
        <translation>種類</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="282"/>
        <source>Label</source>
        <translation>標記</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="283"/>
        <source>Address</source>
        <translation>位址</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="284"/>
        <source>Amount</source>
        <translation>金額</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="285"/>
        <source>ID</source>
        <translation>識別碼</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="289"/>
        <source>Error exporting</source>
        <translation>匯出錯誤</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="289"/>
        <source>Could not write to file %1.</source>
        <translation>無法寫入至 %1 檔案.</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="384"/>
        <source>Range:</source>
        <translation>範圍:</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="392"/>
        <source>to</source>
        <translation>至</translation>
    </message>
</context>
<context>
    <name>VerifyMessageDialog</name>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="14"/>
        <source>Verify Signed Message</source>
        <translation>驗證簽署過的訊息</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="20"/>
        <source>Enter the message and signature below (be careful to correctly copy newlines, spaces, tabs and other invisible characters) to obtain the Bitcoin address used to sign the message.</source>
        <translation>請在下面輸入訊息與簽章(有些字元是看不到的, 如換行, 空格, 跳位符號等, 請小心並正確地複製), 以獲知用來簽署該訊息的位元幣位址.</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="62"/>
        <source>Verify a message and obtain the Bitcoin address used to sign the message</source>
        <translation>驗證一則訊息, 並獲知用來簽署該訊息的位元幣位址</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="65"/>
        <source>&amp;Verify Message</source>
        <translation>驗證訊息</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="79"/>
        <source>Copy the currently selected address to the system clipboard</source>
        <translation>複製目前選取的位址到系統剪貼簿</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="82"/>
        <source>&amp;Copy Address</source>
        <translation>複製位址</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="93"/>
        <source>Reset all verify message fields</source>
        <translation>重置所有訊息驗證欄位</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="96"/>
        <source>Clear &amp;All</source>
        <translation>全部清掉</translation>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="28"/>
        <source>Enter Bitcoin signature</source>
        <translation>輸入位元幣簽章</translation>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="29"/>
        <source>Click &quot;Verify Message&quot; to obtain address</source>
        <translation>按&quot;驗證訊息&quot;來取得位址</translation>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="55"/>
        <location filename="../verifymessagedialog.cpp" line="62"/>
        <source>Invalid Signature</source>
        <translation>無效的簽章</translation>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="55"/>
        <source>The signature could not be decoded. Please check the signature and try again.</source>
        <translation>無法將這個簽章解碼. 請檢查簽章是否正確後再試一次.</translation>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="62"/>
        <source>The signature did not match the message digest. Please check the signature and try again.</source>
        <translation>簽章與訊息的數位摘要不符. 請檢查簽章是否正確後再試一次.</translation>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="72"/>
        <source>Address not found in address book.</source>
        <translation>在位址簿中找不到該位址.</translation>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="72"/>
        <source>Address found in address book: %1</source>
        <translation>在位址簿中找到此位址: %1</translation>
    </message>
</context>
<context>
    <name>WalletModel</name>
    <message>
        <location filename="../walletmodel.cpp" line="158"/>
        <source>Sending...</source>
        <translation>付出中...</translation>
    </message>
</context>
<context>
    <name>WindowOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="313"/>
        <source>Window</source>
        <translation>視窗</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="316"/>
        <source>&amp;Minimize to the tray instead of the taskbar</source>
        <translation>最小化至通知區域而非工作列</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="317"/>
        <source>Show only a tray icon after minimizing the window</source>
        <translation>視窗最小化時只顯示圖示於通知區域</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="320"/>
        <source>M&amp;inimize on close</source>
        <translation>關閉時最小化</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="321"/>
        <source>Minimize instead of exit the application when the window is closed. When this option is enabled, the application will be closed only after selecting Quit in the menu.</source>
        <translation>當視窗關閉時將其最小化, 而非結束應用程式. 當勾選這個選項時, 應用程式只能用選單中的結束來停止執行.</translation>
    </message>
</context>
<context>
    <name>bitcoin-core</name>
    <message>
        <location filename="../bitcoinstrings.cpp" line="43"/>
        <source>Bitcoin version</source>
        <translation>位元幣版本</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="44"/>
        <source>Usage:</source>
        <translation>用法:</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="45"/>
        <source>Send command to -server or bitcoind</source>
        <translation>送指令至 -server 或 bitcoind
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="46"/>
        <source>List commands</source>
        <translation>列出指令
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="47"/>
        <source>Get help for a command</source>
        <translation>取得指令說明
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="49"/>
        <source>Options:</source>
        <translation>選項:
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="50"/>
        <source>Specify configuration file (default: bitcoin.conf)</source>
        <translation>指定設定檔 (預設: bitcoin.conf)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="51"/>
        <source>Specify pid file (default: bitcoind.pid)</source>
        <translation>指定行程識別碼檔案 (預設: bitcoind.pid)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="52"/>
        <source>Generate coins</source>
        <translation>生產位元幣
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="53"/>
        <source>Don&apos;t generate coins</source>
        <translation>不生產位元幣
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="54"/>
        <source>Specify data directory</source>
        <translation>指定資料目錄
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="55"/>
        <source>Set database cache size in megabytes (default: 25)</source>
        <translation>設定資料庫快取大小為多少百萬位元組(MB, 預設: 25)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="56"/>
        <source>Set database disk log size in megabytes (default: 100)</source>
        <translation>設定資料庫的磁碟紀錄大小為多少百萬位元組(MB, 預設: 100)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="57"/>
        <source>Specify connection timeout (in milliseconds)</source>
        <translation>指定連線逾時時間 (毫秒)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="63"/>
        <source>Listen for connections on &lt;port&gt; (default: 8333 or testnet: 18333)</source>
        <translation>在通訊埠 &lt;port&gt; 聽候連線 (預設: 8333, 或若為測試網路: 18333)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="64"/>
        <source>Maintain at most &lt;n&gt; connections to peers (default: 125)</source>
        <translation>維持與節點連線數的上限為 &lt;n&gt; 個 (預設: 125)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="66"/>
        <source>Connect only to the specified node</source>
        <translation>只連線至指定節點
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="67"/>
        <source>Connect to a node to retrieve peer addresses, and disconnect</source>
        <translation>連線到某個節點以取得其它節點的位址, 然後斷線</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="68"/>
        <source>Specify your own public address</source>
        <translation>指定自己公開的位址</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="69"/>
        <source>Only connect to nodes in network &lt;net&gt; (IPv4 or IPv6)</source>
        <translation>只和 &lt;net&gt; 網路上的節點連線 (IPv4 或 IPv6)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="70"/>
        <source>Try to discover public IP address (default: 1)</source>
        <translation>試著找出公開的網際網路位址 (預設: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="73"/>
        <source>Bind to given address. Use [host]:port notation for IPv6</source>
        <translation>與指定的位址繫結. IPv6 要使用 [主機]:通訊埠 的格式</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="75"/>
        <source>Threshold for disconnecting misbehaving peers (default: 100)</source>
        <translation>與亂搞的節點斷線的臨界值 (預設: 100)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="76"/>
        <source>Number of seconds to keep misbehaving peers from reconnecting (default: 86400)</source>
        <translation>避免與亂搞的節點連線的秒數 (預設: 86400)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="79"/>
        <source>Maximum per-connection receive buffer, &lt;n&gt;*1000 bytes (default: 10000)</source>
        <translation>每個連線的接收緩衝區大小上限為 &lt;n&gt;*1000 位元組 (預設: 10000)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="80"/>
        <source>Maximum per-connection send buffer, &lt;n&gt;*1000 bytes (default: 10000)</source>
        <translation>每個連線的傳送緩衝區大小上限為 &lt;n&gt;*1000 位元組 (預設: 10000)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="83"/>
        <source>Detach block and address databases. Increases shutdown time (default: 0)</source>
        <translation>卸載區塊與位址的資料庫. 會延長關閉時間 (預設: 0)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="86"/>
        <source>Accept command line and JSON-RPC commands</source>
        <translation>接受命令列與 JSON-RPC 指令
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="87"/>
        <source>Run in the background as a daemon and accept commands</source>
        <translation>以背景程式執行並接受指令</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="88"/>
        <source>Use the test network</source>
        <translation>使用測試網路
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="89"/>
        <source>Output extra debugging information</source>
        <translation>輸出額外的除錯資訊</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="90"/>
        <source>Prepend debug output with timestamp</source>
        <translation>在除錯輸出內容前附加時間</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="91"/>
        <source>Send trace/debug info to console instead of debug.log file</source>
        <translation>輸出追蹤或除錯資訊至終端機, 而非 debug.log 檔案</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="92"/>
        <source>Send trace/debug info to debugger</source>
        <translation>輸出追蹤或除錯資訊給除錯器</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="93"/>
        <source>Username for JSON-RPC connections</source>
        <translation>JSON-RPC 連線使用者名稱</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="94"/>
        <source>Password for JSON-RPC connections</source>
        <translation>JSON-RPC 連線密碼</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="95"/>
        <source>Listen for JSON-RPC connections on &lt;port&gt; (default: 8332)</source>
        <translation>在通訊埠 &lt;port&gt; 聽候 JSON-RPC 連線 (預設: 8332)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="96"/>
        <source>Allow JSON-RPC connections from specified IP address</source>
        <translation>只允許從指定網路位址來的 JSON-RPC 連線</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="97"/>
        <source>Send commands to node running on &lt;ip&gt; (default: 127.0.0.1)</source>
        <translation>送指令給在 &lt;ip&gt; 的節點 (預設: 127.0.0.1)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="98"/>
        <source>Execute command when the best block changes (%s in cmd is replaced by block hash)</source>
        <translation>當最新區塊改變時所要執行的指令 (指令中的 %s 會被取代為區塊的雜湊值)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="101"/>
        <source>Upgrade wallet to latest format</source>
        <translation>將錢包升級成最新的格式</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="102"/>
        <source>Set key pool size to &lt;n&gt; (default: 100)</source>
        <translation>設定密鑰池大小為 &lt;n&gt; (預設: 100)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="103"/>
        <source>Rescan the block chain for missing wallet transactions</source>
        <translation>重新掃描區塊鎖鏈, 以尋找錢包所遺漏的交易.</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="104"/>
        <source>How many blocks to check at startup (default: 2500, 0 = all)</source>
        <translation>啓動時檢查多少區塊 (預設: 2500, 0 表示全部)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="105"/>
        <source>How thorough the block verification is (0-6, default: 1)</source>
        <translation>區塊檢查的仔細程度 (0 至 6, 預設: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="106"/>
        <source>Imports blocks from external blk000?.dat file</source>
        <translation>從外來的區塊檔 blk000?.dat 匯入區塊</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="108"/>
        <source>
SSL options: (see the Bitcoin Wiki for SSL setup instructions)</source>
        <translation>
SSL 選項: (SSL 設定程序請見 Bitcoin Wiki)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="111"/>
        <source>Use OpenSSL (https) for JSON-RPC connections</source>
        <translation>使用 OpenSSL (https) 於JSON-RPC 連線
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="112"/>
        <source>Server certificate file (default: server.cert)</source>
        <translation>伺服器憑證檔 (預設: server.cert)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="113"/>
        <source>Server private key (default: server.pem)</source>
        <translation>伺服器密鑰檔 (預設: server.pem)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="114"/>
        <source>Acceptable ciphers (default: TLSv1+HIGH:!SSLv2:!aNULL:!eNULL:!AH:!3DES:@STRENGTH)</source>
        <translation>可以接受的加密法 (預設: TLSv1+HIGH:!SSLv2:!aNULL:!eNULL:!AH:!3DES:@STRENGTH)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="145"/>
        <source>Warning: Disk space is low</source>
        <translation>警告: 磁碟空間很少</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="107"/>
        <source>This help message</source>
        <translation>此協助訊息
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="121"/>
        <source>Cannot obtain a lock on data directory %s.  Bitcoin is probably already running.</source>
        <translation>無法鎖定資料目錄 %s. 也許位元幣已經在執行了.</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="48"/>
        <source>Bitcoin</source>
        <translation>位元幣</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="30"/>
        <source>Unable to bind to %s on this computer (bind returned error %d, %s)</source>
        <translation>無法和這台電腦上的 %s 繫結 (繫結回傳錯誤 %d, %s)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="58"/>
        <source>Connect through socks proxy</source>
        <translation>透過 SOCKS 代理伺服器連線</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="59"/>
        <source>Select the version of socks proxy to use (4 or 5, 5 is default)</source>
        <translation>選擇 SOCKS 代理伺服器的協定版本(4 或 5, 預設是 5)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="60"/>
        <source>Do not use proxy for connections to network &lt;net&gt; (IPv4 or IPv6)</source>
        <translation>不透過 SOCKS 代理伺服器連線至 &lt;net&gt; 網路 (IPv4 或 IPv6)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="61"/>
        <source>Allow DNS lookups for -addnode, -seednode and -connect</source>
        <translation>允許對 -addnode, -seednode, -connect 的參數使用域名查詢 </translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="62"/>
        <source>Pass DNS requests to (SOCKS5) proxy</source>
        <translation>透過 (SOCKS5) 代理伺服器送出域名查詢</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="142"/>
        <source>Loading addresses...</source>
        <translation>載入位址中...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="132"/>
        <source>Error loading blkindex.dat</source>
        <translation>載入 blkindex.dat 失敗</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="134"/>
        <source>Error loading wallet.dat: Wallet corrupted</source>
        <translation>載入檔案 wallet.dat 失敗: 錢包壞掉了</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="135"/>
        <source>Error loading wallet.dat: Wallet requires newer version of Bitcoin</source>
        <translation>載入檔案 wallet.dat 失敗: 此錢包需要新版的 Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="136"/>
        <source>Wallet needed to be rewritten: restart Bitcoin to complete</source>
        <translation>錢包需要重寫: 請重啟位元幣來完成</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="137"/>
        <source>Error loading wallet.dat</source>
        <translation>載入檔案 wallet.dat 失敗</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="124"/>
        <source>Invalid -proxy address: &apos;%s&apos;</source>
        <translation>無效的 -proxy 位址: &apos;%s&apos;</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="125"/>
        <source>Unknown network specified in -noproxy: &apos;%s&apos;</source>
        <translation>在  -noproxy 指定了不明的網路別: &apos;%s&apos;</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="127"/>
        <source>Unknown network specified in -onlynet: &apos;%s&apos;</source>
        <translation>在 -onlynet 指定了不明的網路別: &apos;%s&apos;</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="126"/>
        <source>Unknown -socks proxy version requested: %i</source>
        <translation>在 -socks 指定了不明的代理協定版本: %i</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="128"/>
        <source>Cannot resolve -bind address: &apos;%s&apos;</source>
        <translation>無法解析 -bind 位址: &apos;%s&apos;</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="129"/>
        <source>Not listening on any port</source>
        <translation>不在任何通訊埠聽候連線</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="130"/>
        <source>Cannot resolve -externalip address: &apos;%s&apos;</source>
        <translation>無法解析 -externalip 位址: &apos;%s&apos;</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="117"/>
        <source>Invalid amount for -paytxfee=&lt;amount&gt;: &apos;%s&apos;</source>
        <translation>設定 -paytxfee=&lt;金額&gt; 的金額無效: &apos;%s&apos;</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="143"/>
        <source>Error: could not start node</source>
        <translation>錯誤: 無法啓動節點</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="31"/>
        <source>Error: Wallet locked, unable to create transaction  </source>
        <translation>錯誤: 錢包被上鎖了, 無法產生新的交易 </translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="32"/>
        <source>Error: This transaction requires a transaction fee of at least %s because of its amount, complexity, or use of recently received funds  </source>
        <translation>錯誤: 這筆交易需要至少 %s 的手續費, 因為它的金額太大, 或複雜度太高, 或是使用了最近才剛收到的款項 </translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="35"/>
        <source>Error: Transaction creation failed  </source>
        <translation>錯誤: 交易產生失敗</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="36"/>
        <source>Sending...</source>
        <translation>付出中...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="37"/>
        <source>Error: The transaction was rejected.  This might happen if some of the coins in your wallet were already spent, such as if you used a copy of wallet.dat and coins were spent in the copy but not marked as spent here.</source>
        <translation>錯誤: 交易被拒絕. 有時候會發生這種錯誤, 是因為你錢包中的一些錢已經被花掉了. 比如說你複製了錢包檔 wallet.dat, 然後用複製的錢包花掉了錢, 你現在所用的原來的錢包中卻沒有該筆交易紀錄.</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="41"/>
        <source>Invalid amount</source>
        <translation>無效的金額</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="42"/>
        <source>Insufficient funds</source>
        <translation>累積金額不足</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="131"/>
        <source>Loading block index...</source>
        <translation>載入區塊索引中...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="65"/>
        <source>Add a node to connect to and attempt to keep the connection open</source>
        <translation>加入一個要連線的節線, 並試著保持對它的連線暢通</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="28"/>
        <source>Unable to bind to %s on this computer. Bitcoin is probably already running.</source>
        <translation>無法和這台電腦上的 %s 繫結. 也許位元幣已經在執行了.</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="71"/>
        <source>Find peers using internet relay chat (default: 0)</source>
        <translation>是否使用網際網路中繼聊天(IRC)來找節點 (預設: 0)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="72"/>
        <source>Accept connections from outside (default: 1)</source>
        <translation>是否接受外來連線 (預設: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="74"/>
        <source>Find peers using DNS lookup (default: 1)</source>
        <translation>是否允許在找節點時使用域名查詢 (預設: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="81"/>
        <source>Use Universal Plug and Play to map the listening port (default: 1)</source>
        <translation>是否使用通用即插即用(UPnP)來設定聽候連線的通訊埠 (預設: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="82"/>
        <source>Use Universal Plug and Play to map the listening port (default: 0)</source>
        <translation>是否使用通用即插即用(UPnP)來設定聽候連線的通訊埠 (預設: 0)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="85"/>
        <source>Fee per KB to add to transactions you send</source>
        <translation>交易付款時每 KB 的交易手續費</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="118"/>
        <source>Warning: -paytxfee is set very high. This is the transaction fee you will pay if you send a transaction.</source>
        <translation>警告: -paytxfee 設定了很高的金額. 這可是你交易付款所要付的手續費.</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="133"/>
        <source>Loading wallet...</source>
        <translation>載入錢包中...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="138"/>
        <source>Cannot downgrade wallet</source>
        <translation>無法將錢包格式降級</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="139"/>
        <source>Cannot initialize keypool</source>
        <translation>無法將密鑰池初始化</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="140"/>
        <source>Cannot write default address</source>
        <translation>無法寫入預設位址</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="141"/>
        <source>Rescanning...</source>
        <translation>重新掃描中...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="144"/>
        <source>Done loading</source>
        <translation>載入完成</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="8"/>
        <source>To use the %s option</source>
        <translation>為了要使用 %s 選項</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="9"/>
        <source>%s, you must set a rpcpassword in the configuration file:
 %s
It is recommended you use the following random password:
rpcuser=bitcoinrpc
rpcpassword=%s
(you do not need to remember this password)
If the file does not exist, create it with owner-readable-only file permissions.
</source>
        <translation>%s, 你必須在下列設定檔中設定 RPC 密碼(rpcpassword):
%s
建議你使用下列的隨機產生密碼:
rpcuser=bitcoinrpc
rpcpassword=%s
(你不用記住這個密碼)
如果這個檔案還不存在, 請在新增時設定檔案權限為只有擁有者才能讀取.
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="18"/>
        <source>Error</source>
        <translation>錯誤</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="19"/>
        <source>An error occured while setting up the RPC port %i for listening: %s</source>
        <translation>設定聽候 RPC 連線的通訊埠 %i 時發生錯誤: %s</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="20"/>
        <source>You must set rpcpassword=&lt;password&gt; in the configuration file:
%s
If the file does not exist, create it with owner-readable-only file permissions.</source>
        <translation>你必須在下列設定檔中設定 RPC 密碼(rpcpassword=&lt;password&gt;):
%s
如果這個檔案還不存在, 請在新增時設定檔案權限為只有擁有者才能讀取.</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="25"/>
        <source>Warning: Please check that your computer&apos;s date and time are correct.  If your clock is wrong Bitcoin will not work properly.</source>
        <translation>警告: 請檢查電腦時間日期是否正確. 位元幣無法在時鐘不準的情況下正常運作.</translation>
    </message>
</context>
</TS>