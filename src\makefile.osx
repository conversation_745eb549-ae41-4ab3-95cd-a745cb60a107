# -*- mode: Makefile; -*-
# Copyright (c) 2011 Bitcoin Developers
# Distributed under the MIT/X11 software license, see the accompanying
# file license.txt or http://www.opensource.org/licenses/mit-license.php.

# Mac OS X makefile for testcoin
# Originally by <PERSON><PERSON><PERSON> (<EMAIL>)

CXX=llvm-g++
DEPSDIR=/opt/local

INCLUDEPATHS= \
 -I"$(CURDIR)" \
 -I"$(CURDIR)"/obj \
 -I"$(DEPSDIR)/include" \
 -I"$(DEPSDIR)/include/db48"

LIBPATHS= \
 -L"$(DEPSDIR)/lib" \
 -L"$(DEPSDIR)/lib/db48"

USE_UPNP:=1

LIBS= -dead_strip

TESTDEFS = -DTEST_DATA_DIR=$(abspath test/data)

ifdef STATIC
# Build STATIC if you are redistributing the testcoind binary
TESTLIBS += \
 $(DEPSDIR)/lib/libboost_unit_test_framework-mt.a
LIBS += \
 $(DEPSDIR)/lib/db48/libdb_cxx-4.8.a \
 $(DEPSDIR)/lib/libboost_system-mt.a \
 $(DEPSDIR)/lib/libboost_filesystem-mt.a \
 $(DEPSDIR)/lib/libboost_program_options-mt.a \
 $(DEPSDIR)/lib/libboost_thread-mt.a \
 $(DEPSDIR)/lib/libssl.a \
 $(DEPSDIR)/lib/libcrypto.a \
 -lz
else
TESTLIBS += \
 -lboost_unit_test_framework-mt
LIBS += \
 -ldb_cxx-4.8 \
 -lboost_system-mt \
 -lboost_filesystem-mt \
 -lboost_program_options-mt \
 -lboost_thread-mt \
 -lssl \
 -lcrypto \
 -lz
TESTDEFS += -DBOOST_TEST_DYN_LINK
endif

DEFS=-DMAC_OSX -DMSG_NOSIGNAL=0 -DBOOST_SPIRIT_THREADSAFE -DUSE_IPV6

ifdef RELEASE
# Compile for maximum compatibility and smallest size.
# This requires that dependencies are compiled
# the same way.
CFLAGS = -mmacosx-version-min=10.5 -arch i386 -O3
else
CFLAGS = -g
endif

# ppc doesn't work because we don't support big-endian
CFLAGS += -Wall -Wextra -Wformat -Wformat-security -Wno-unused-parameter \
    $(DEBUGFLAGS) $(DEFS) $(INCLUDEPATHS)

OBJS= \
    obj/version.o \
    obj/checkpoints.o \
    obj/netbase.o \
    obj/addrman.o \
    obj/crypter.o \
    obj/key.o \
    obj/db.o \
    obj/init.o \
    obj/irc.o \
    obj/keystore.o \
    obj/main.o \
    obj/net.o \
    obj/protocol.o \
    obj/bitcoinrpc.o \
    obj/rpcdump.o \
    obj/rpcnet.o \
    obj/rpcrawtransaction.o \
    obj/script.o \
    obj/scrypt.o \
    obj/sync.o \
    obj/util.o \
    obj/wallet.o \
    obj/walletdb.o \
    obj/noui.o

ifdef USE_UPNP
	DEFS += -DUSE_UPNP=$(USE_UPNP)
ifdef STATIC
	LIBS += $(DEPSDIR)/lib/libminiupnpc.a
else
	LIBS += -lminiupnpc
endif
endif

all: bellsd

# auto-generated dependencies:
-include obj/*.P
-include obj-test/*.P

obj/scrypt.o: scrypt.c
	gcc -c $(CFLAGS) -MMD -o $@ $<
	@cp $(@:%.o=%.d) $(@:%.o=%.P); \
	  sed -e 's/#.*//' -e 's/^[^:]*: *//' -e 's/ *\\$$//' \
	      -e '/^$$/ d' -e 's/$$/ :/' < $(@:%.o=%.d) >> $(@:%.o=%.P); \
	  rm -f $(@:%.o=%.d)

obj/build.h: FORCE
	/bin/sh ../share/genbuild.sh obj/build.h
version.cpp: obj/build.h
DEFS += -DHAVE_BUILD_INFO

obj/%.o: %.cpp
	$(CXX) -c $(CFLAGS) -MMD -MF $(@:%.o=%.d) -o $@ $<
	@cp $(@:%.o=%.d) $(@:%.o=%.P); \
	  sed -e 's/#.*//' -e 's/^[^:]*: *//' -e 's/ *\\$$//' \
	      -e '/^$$/ d' -e 's/$$/ :/' < $(@:%.o=%.d) >> $(@:%.o=%.P); \
	  rm -f $(@:%.o=%.d)

bellsd: $(OBJS:obj/%=obj/%)
	$(CXX) $(CFLAGS) -o $@ $(LIBPATHS) $^ $(LIBS)

TESTOBJS := $(patsubst test/%.cpp,obj-test/%.o,$(wildcard test/*.cpp))

obj-test/%.o: test/%.cpp
	$(CXX) -c $(TESTDEFS) $(CFLAGS) -MMD -MF $(@:%.o=%.d) -o $@ $<
	@cp $(@:%.o=%.d) $(@:%.o=%.P); \
	  sed -e 's/#.*//' -e 's/^[^:]*: *//' -e 's/ *\\$$//' \
	      -e '/^$$/ d' -e 's/$$/ :/' < $(@:%.o=%.d) >> $(@:%.o=%.P); \
	  rm -f $(@:%.o=%.d)

test_bells: $(TESTOBJS) $(filter-out obj/init.o,$(OBJS:obj/%=obj/%))
	$(CXX) $(CFLAGS) -o $@ $(LIBPATHS) $^ $(LIBS) $(TESTLIBS)

clean:
	-rm -f bellsd test_bells
	-rm -f obj/*.o
	-rm -f obj-test/*.o
	-rm -f obj/*.P
	-rm -f obj-test/*.P
	-rm -f src/build.h

FORCE:
