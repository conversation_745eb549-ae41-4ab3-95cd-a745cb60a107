#!/usr/bin/env python3
"""
Bellscoin Treasure Hunt - Deep Analysis Script
==============================================

This script performs comprehensive analysis of the most promising leads:
1. Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
2. "Nintondo" Message

Author: Treasure Hunt Analysis Team
Date: 2025
"""

import hashlib
import binascii
import base58
import struct
import re
from typing import List, Tuple, Optional
import itertools

# Genesis block parameters
GENESIS_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"
GENESIS_MESSAGE = "Nintondo"
GENESIS_TIMESTAMP = 1383465600  # November 3, 2013
GENESIS_NONCE = 2084524493
GENESIS_MERKLE_ROOT = "97ddfbbae6be97fd6cdf3e7ca13232a3afff2353e29badfab7f73011edd4ced9"

def print_section(title: str):
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_subsection(title: str):
    """Print a formatted subsection header."""
    print(f"\n{'-'*40}")
    print(f" {title}")
    print(f"{'-'*40}")

def hex_to_ascii(hex_string: str) -> str:
    """Convert hex string to ASCII, replacing non-printable chars with dots."""
    try:
        bytes_data = bytes.fromhex(hex_string)
        return ''.join(chr(b) if 32 <= b <= 126 else '.' for b in bytes_data)
    except ValueError:
        return "Invalid hex"

def analyze_genesis_pubkey():
    """Comprehensive analysis of the genesis public key."""
    print_section("GENESIS PUBLIC KEY ANALYSIS")
    
    # Remove '04' prefix (uncompressed key indicator)
    pubkey_coords = GENESIS_PUBKEY[2:]
    
    # Split into X and Y coordinates (32 bytes each)
    x_coord = pubkey_coords[:64]
    y_coord = pubkey_coords[64:]
    
    print(f"Full Public Key: {GENESIS_PUBKEY}")
    print(f"Key Type: {'Uncompressed' if GENESIS_PUBKEY.startswith('04') else 'Compressed'}")
    print(f"X Coordinate: {x_coord}")
    print(f"Y Coordinate: {y_coord}")
    
    # Analyze coordinates separately
    print_subsection("X Coordinate Analysis")
    analyze_hex_segment(x_coord, "X")
    
    print_subsection("Y Coordinate Analysis")
    analyze_hex_segment(y_coord, "Y")
    
    # Look for patterns in the full key
    print_subsection("Full Key Pattern Analysis")
    analyze_hex_patterns(GENESIS_PUBKEY)
    
    # Try ASCII interpretation
    print_subsection("ASCII Interpretation Attempts")
    print(f"Full key as ASCII: {hex_to_ascii(GENESIS_PUBKEY)}")
    print(f"X coordinate as ASCII: {hex_to_ascii(x_coord)}")
    print(f"Y coordinate as ASCII: {hex_to_ascii(y_coord)}")
    
    # Generate addresses
    print_subsection("Address Generation")
    generate_addresses_from_pubkey(GENESIS_PUBKEY)

def analyze_hex_segment(hex_string: str, name: str):
    """Analyze a hex string segment for patterns."""
    print(f"{name} Length: {len(hex_string)} characters ({len(hex_string)//2} bytes)")
    
    # Count digit frequencies
    digit_counts = {}
    for digit in hex_string.lower():
        digit_counts[digit] = digit_counts.get(digit, 0) + 1
    
    print(f"{name} Digit frequencies:")
    for digit in "0123456789abcdef":
        count = digit_counts.get(digit, 0)
        print(f"  {digit}: {count:2d} ({count/len(hex_string)*100:.1f}%)")
    
    # Look for repeated patterns
    print(f"{name} Repeated patterns:")
    for length in [2, 3, 4, 6, 8]:
        patterns = {}
        for i in range(0, len(hex_string) - length + 1, 2):
            pattern = hex_string[i:i+length]
            if len(pattern) == length:
                patterns[pattern] = patterns.get(pattern, 0) + 1
        
        repeated = {p: c for p, c in patterns.items() if c > 1}
        if repeated:
            print(f"  {length}-char patterns: {repeated}")

def analyze_hex_patterns(hex_string: str):
    """Look for mathematical patterns in hex string."""
    print("Looking for mathematical patterns...")
    
    # Convert to integer for mathematical analysis
    try:
        as_int = int(hex_string, 16)
        print(f"As integer: {as_int}")
        print(f"Bit length: {as_int.bit_length()}")
        
        # Check if it's close to powers of 2
        for i in range(250, 270):
            power_of_2 = 2**i
            diff = abs(as_int - power_of_2)
            if diff < power_of_2 // 1000:  # Within 0.1%
                print(f"Close to 2^{i}: difference = {diff}")
        
        # Check for known mathematical constants
        check_mathematical_constants(as_int)
        
    except ValueError:
        print("Could not convert to integer")

def check_mathematical_constants(value: int):
    """Check if value is related to known mathematical constants."""
    import math
    
    constants = {
        "π": math.pi,
        "e": math.e,
        "φ (golden ratio)": (1 + math.sqrt(5)) / 2,
        "√2": math.sqrt(2),
        "√3": math.sqrt(3),
        "√5": math.sqrt(5),
    }
    
    print("Checking against mathematical constants:")
    for name, const in constants.items():
        # Scale constant to similar magnitude
        for scale in [10**i for i in range(70, 80)]:
            scaled = int(const * scale)
            if abs(value - scaled) < scaled // 1000:
                print(f"  Close to {name} * 10^{scale.bit_length()-1}")

def generate_addresses_from_pubkey(pubkey_hex: str):
    """Generate various cryptocurrency addresses from public key."""
    try:
        pubkey_bytes = bytes.fromhex(pubkey_hex)
        
        # Bitcoin addresses
        print("Bitcoin addresses:")
        btc_addr = pubkey_to_bitcoin_address(pubkey_bytes)
        print(f"  P2PKH: {btc_addr}")
        
        # Try compressed version
        if pubkey_hex.startswith('04'):
            compressed_pubkey = compress_pubkey(pubkey_bytes)
            btc_compressed = pubkey_to_bitcoin_address(compressed_pubkey)
            print(f"  P2PKH (compressed): {btc_compressed}")
        
        # Dogecoin addresses (similar to Bitcoin but different version byte)
        print("Dogecoin addresses:")
        doge_addr = pubkey_to_dogecoin_address(pubkey_bytes)
        print(f"  P2PKH: {doge_addr}")
        
        # Bellscoin addresses (assuming similar to Bitcoin)
        print("Bellscoin addresses:")
        bells_addr = pubkey_to_bellscoin_address(pubkey_bytes)
        print(f"  P2PKH: {bells_addr}")
        
    except Exception as e:
        print(f"Error generating addresses: {e}")

def compress_pubkey(pubkey_bytes: bytes) -> bytes:
    """Convert uncompressed public key to compressed format."""
    if len(pubkey_bytes) != 65 or pubkey_bytes[0] != 0x04:
        return pubkey_bytes
    
    x = pubkey_bytes[1:33]
    y = pubkey_bytes[33:65]
    
    # Check if Y is even or odd
    y_int = int.from_bytes(y, 'big')
    prefix = 0x02 if y_int % 2 == 0 else 0x03
    
    return bytes([prefix]) + x

def pubkey_to_bitcoin_address(pubkey_bytes: bytes) -> str:
    """Convert public key to Bitcoin address."""
    # SHA256 then RIPEMD160
    sha256_hash = hashlib.sha256(pubkey_bytes).digest()
    ripemd160_hash = hashlib.new('ripemd160', sha256_hash).digest()
    
    # Add version byte (0x00 for Bitcoin mainnet)
    versioned_hash = b'\x00' + ripemd160_hash
    
    # Double SHA256 for checksum
    checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
    
    # Base58 encode
    return base58.b58encode(versioned_hash + checksum).decode()

def pubkey_to_dogecoin_address(pubkey_bytes: bytes) -> str:
    """Convert public key to Dogecoin address."""
    sha256_hash = hashlib.sha256(pubkey_bytes).digest()
    ripemd160_hash = hashlib.new('ripemd160', sha256_hash).digest()
    
    # Add version byte (0x1e for Dogecoin mainnet)
    versioned_hash = b'\x1e' + ripemd160_hash
    
    checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
    return base58.b58encode(versioned_hash + checksum).decode()

def pubkey_to_bellscoin_address(pubkey_bytes: bytes) -> str:
    """Convert public key to Bellscoin address (assuming Bitcoin-like)."""
    sha256_hash = hashlib.sha256(pubkey_bytes).digest()
    ripemd160_hash = hashlib.new('ripemd160', sha256_hash).digest()
    
    # Assuming version byte 0x19 for Bellscoin (this is a guess)
    versioned_hash = b'\x19' + ripemd160_hash
    
    checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
    return base58.b58encode(versioned_hash + checksum).decode()

def analyze_nintondo():
    """Comprehensive analysis of the 'Nintondo' message."""
    print_section("NINTONDO MESSAGE ANALYSIS")
    
    message = GENESIS_MESSAGE
    print(f"Original message: '{message}'")
    print(f"Length: {len(message)} characters")
    print(f"ASCII values: {[ord(c) for c in message]}")
    print(f"Hex representation: {message.encode().hex()}")
    
    # Cipher analysis
    print_subsection("Cipher Analysis")
    analyze_ciphers(message)
    
    # Anagram analysis
    print_subsection("Anagram Analysis")
    analyze_anagrams(message)
    
    # Reference analysis
    print_subsection("Reference Analysis")
    analyze_references(message)
    
    # Key derivation attempts
    print_subsection("Key Derivation from Nintondo")
    derive_keys_from_nintondo(message)

def analyze_ciphers(message: str):
    """Try various cipher techniques on the message."""
    print("Trying common ciphers:")
    
    # Caesar cipher (all shifts)
    print("Caesar cipher attempts:")
    for shift in range(1, 26):
        result = caesar_cipher(message, shift)
        print(f"  Shift {shift:2d}: {result}")
    
    # ROT13
    try:
        import codecs
        rot13_result = codecs.decode(message, 'rot13') if message.isalpha() else 'N/A (contains non-alpha)'
        print(f"ROT13: {rot13_result}")
    except Exception as e:
        print(f"ROT13: Error - {e}")
    
    # Atbash cipher
    print(f"Atbash: {atbash_cipher(message)}")
    
    # Reverse
    print(f"Reversed: {message[::-1]}")

def caesar_cipher(text: str, shift: int) -> str:
    """Apply Caesar cipher with given shift."""
    result = ""
    for char in text:
        if char.isalpha():
            ascii_offset = 65 if char.isupper() else 97
            shifted = (ord(char) - ascii_offset + shift) % 26
            result += chr(shifted + ascii_offset)
        else:
            result += char
    return result

def atbash_cipher(text: str) -> str:
    """Apply Atbash cipher (A=Z, B=Y, etc.)."""
    result = ""
    for char in text:
        if char.isalpha():
            if char.isupper():
                result += chr(90 - (ord(char) - 65))
            else:
                result += chr(122 - (ord(char) - 97))
        else:
            result += char
    return result

def analyze_anagrams(message: str):
    """Analyze potential anagrams of the message."""
    print(f"Analyzing anagrams of '{message}':")
    
    # Check if it's an anagram of common words
    common_words = [
        "nintendo", "donation", "not", "don", "into", "onto", "ton", "dot", "nod",
        "din", "tin", "ion", "odo", "dodo", "noon", "toon", "indo", "dino"
    ]
    
    message_sorted = sorted(message.lower())
    
    print("Possible anagram matches:")
    for word in common_words:
        if sorted(word.lower()) == message_sorted:
            print(f"  ✓ '{word}' is an anagram!")
        elif set(word.lower()).issubset(set(message.lower())):
            print(f"  ~ '{word}' uses subset of letters")

def analyze_references(message: str):
    """Analyze potential references in the message."""
    print("Analyzing potential references:")
    
    # 2013 context
    print("2013 context analysis:")
    print("  - Nintendo released Pokemon X/Y in October 2013")
    print("  - Nintendo 3DS was popular")
    print("  - Wii U was released in late 2012")
    print("  - Bitcoin was gaining mainstream attention")
    
    # Variations
    variations = [
        "Nintendo", "Nintondo", "Nintend0", "N1ntondo", "Nintondo2013",
        "Nintondo_2013", "nintondo", "NINTONDO"
    ]
    
    print("Potential variations to test:")
    for var in variations:
        print(f"  - {var}")

def derive_keys_from_nintondo(message: str):
    """Attempt key derivation using Nintondo as seed."""
    print("Attempting key derivation from Nintondo:")
    
    variations = [
        message,
        message.lower(),
        message.upper(),
        "Nintendo",
        f"{message}{GENESIS_TIMESTAMP}",
        f"{message}_{GENESIS_TIMESTAMP}",
        f"{message}{GENESIS_NONCE}",
        f"{GENESIS_TIMESTAMP}_{message}",
        f"{message}_2013",
        f"{message}2013",
    ]
    
    for i, variation in enumerate(variations):
        print(f"\nVariation {i+1}: '{variation}'")
        
        # Try different hash functions
        hash_functions = [
            ('SHA256', hashlib.sha256),
            ('SHA1', hashlib.sha1),
            ('MD5', hashlib.md5),
            ('SHA512', hashlib.sha512),
        ]
        
        for name, hash_func in hash_functions:
            try:
                hash_result = hash_func(variation.encode()).hexdigest()
                print(f"  {name}: {hash_result}")
                
                # Check if this could be related to our genesis key
                if hash_result.startswith(GENESIS_PUBKEY[:8]):
                    print(f"    ⚠️  Starts with same 8 chars as genesis key!")
                
            except Exception as e:
                print(f"  {name}: Error - {e}")

def analyze_genesis_relationships():
    """Analyze mathematical relationships between genesis parameters."""
    print_section("GENESIS PARAMETER RELATIONSHIPS")
    
    params = {
        "Timestamp": GENESIS_TIMESTAMP,
        "Nonce": GENESIS_NONCE,
        "Merkle Root": GENESIS_MERKLE_ROOT,
        "Public Key": GENESIS_PUBKEY,
    }
    
    print("Genesis parameters:")
    for name, value in params.items():
        print(f"  {name}: {value}")
    
    # XOR operations
    print_subsection("XOR Analysis")
    try:
        timestamp_hex = hex(GENESIS_TIMESTAMP)[2:]
        nonce_hex = hex(GENESIS_NONCE)[2:]
        
        print(f"Timestamp as hex: {timestamp_hex}")
        print(f"Nonce as hex: {nonce_hex}")
        
        # XOR timestamp and nonce
        ts_int = GENESIS_TIMESTAMP
        nonce_int = GENESIS_NONCE
        xor_result = ts_int ^ nonce_int
        print(f"Timestamp XOR Nonce: {xor_result} (hex: {hex(xor_result)})")
        
        # Try XOR with parts of merkle root and pubkey
        merkle_int = int(GENESIS_MERKLE_ROOT[:8], 16)
        pubkey_int = int(GENESIS_PUBKEY[:8], 16)
        
        print(f"First 8 chars of merkle as int: {merkle_int}")
        print(f"First 8 chars of pubkey as int: {pubkey_int}")
        print(f"Merkle XOR Pubkey (first 8): {merkle_int ^ pubkey_int}")
        
    except Exception as e:
        print(f"Error in XOR analysis: {e}")
    
    # Look for timestamp significance
    print_subsection("Timestamp Analysis")
    analyze_timestamp()

def analyze_timestamp():
    """Analyze the genesis timestamp for significance."""
    import datetime
    
    timestamp = GENESIS_TIMESTAMP
    dt = datetime.datetime.fromtimestamp(timestamp, tz=datetime.timezone.utc)
    
    print(f"Genesis timestamp: {timestamp}")
    print(f"Date/time: {dt.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print(f"Day of week: {dt.strftime('%A')}")
    print(f"Day of year: {dt.timetuple().tm_yday}")
    
    # Check for special dates
    print("Special date analysis:")
    if dt.month == 11 and dt.day == 3:
        print("  ✓ November 3rd - Day after Halloween")
        print("  ✓ Could be significant in 2013 context")
    
    # Check if it's related to other crypto launches
    print("  - Bitcoin genesis: January 3, 2009")
    print("  - Litecoin genesis: October 7, 2011")
    print("  - Dogecoin launch: December 6, 2013 (after Bellscoin)")

def comprehensive_key_search():
    """Perform comprehensive search for the private key."""
    print_section("COMPREHENSIVE KEY SEARCH")
    
    print("Testing systematic approaches to find the private key...")
    
    # Test if Nintondo variations could be the private key
    print_subsection("Direct Private Key Tests")
    test_nintondo_as_private_key()
    
    # Test mathematical combinations
    print_subsection("Mathematical Combination Tests")
    test_mathematical_combinations()

def test_nintondo_as_private_key():
    """Test if Nintondo variations could directly be the private key."""
    variations = [
        GENESIS_MESSAGE,
        "Nintendo",
        f"{GENESIS_MESSAGE}2013",
        f"2013{GENESIS_MESSAGE}",
        f"{GENESIS_TIMESTAMP}",
        f"{GENESIS_NONCE}",
    ]
    
    for variation in variations:
        print(f"Testing '{variation}' as private key seed:")
        
        # Hash to get potential private key
        private_key_hash = hashlib.sha256(variation.encode()).digest()
        private_key_hex = private_key_hash.hex()
        
        print(f"  SHA256 hash: {private_key_hex}")
        
        # Try to derive public key (simplified - would need full ECC implementation)
        print(f"  Would need ECC multiplication to verify against: {GENESIS_PUBKEY[:20]}...")

def test_mathematical_combinations():
    """Test mathematical combinations of genesis parameters."""
    print("Testing mathematical combinations:")
    
    # Simple arithmetic with timestamp and nonce
    combinations = [
        GENESIS_TIMESTAMP + GENESIS_NONCE,
        GENESIS_TIMESTAMP - GENESIS_NONCE,
        GENESIS_TIMESTAMP * GENESIS_NONCE,
        GENESIS_TIMESTAMP ^ GENESIS_NONCE,
    ]
    
    for i, combo in enumerate(combinations):
        print(f"  Combination {i+1}: {combo} (hex: {hex(combo)})")
        
        # Hash the combination
        combo_hash = hashlib.sha256(str(combo).encode()).hexdigest()
        print(f"    SHA256: {combo_hash}")

def main():
    """Main analysis function."""
    print("BELLSCOIN TREASURE HUNT - DEEP ANALYSIS")
    print("=" * 60)
    print("Analyzing the most promising leads from the treasure hunt")
    print("Focus: Genesis Public Key and 'Nintondo' message")
    print()
    
    try:
        # Analyze the genesis public key
        analyze_genesis_pubkey()
        
        # Analyze the Nintondo message
        analyze_nintondo()
        
        # Analyze relationships between genesis parameters
        analyze_genesis_relationships()
        
        # Comprehensive key search
        comprehensive_key_search()
        
        print_section("ANALYSIS COMPLETE")
        print("Review the output above for any significant patterns or findings.")
        print("Pay special attention to:")
        print("  - Any ASCII interpretations that make sense")
        print("  - Mathematical patterns in the public key")
        print("  - Cipher results that produce meaningful text")
        print("  - Address generation results")
        print("  - XOR operation results")
        print("  - Any matches between derived keys and genesis parameters")
        
    except Exception as e:
        print(f"Error during analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()