<?xml version="1.0" ?><!DOCTYPE TS><TS language="bg" version="2.0">
<defaultcodec>UTF-8</defaultcodec>
<context>
    <name>AboutDialog</name>
    <message>
        <location filename="../forms/aboutdialog.ui" line="14"/>
        <source>About Bitcoin</source>
        <translation>За Биткоин</translation>
    </message>
    <message>
        <location filename="../forms/aboutdialog.ui" line="53"/>
        <source>&lt;b&gt;Bitcoin&lt;/b&gt; version</source>
        <translation>&lt;b&gt;Биткоин&lt;/b&gt; версия</translation>
    </message>
    <message>
        <location filename="../forms/aboutdialog.ui" line="97"/>
        <source>Copyright © 2009-2012 Bitcoin Developers

This is experimental software.

Distributed under the MIT/X11 software license, see the accompanying file license.txt or http://www.opensource.org/licenses/mit-license.php.

This product includes software developed by the OpenSSL Project for use in the OpenSSL Toolkit (http://www.openssl.org/) and cryptographic software written by <PERSON> (<EMAIL>) and UPnP software written by <PERSON>.</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>AddressBookPage</name>
    <message>
        <location filename="../forms/addressbookpage.ui" line="14"/>
        <source>Address Book</source>
        <translation>Адреси</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="20"/>
        <source>These are your Bitcoin addresses for receiving payments.  You may want to give a different one to each sender so you can keep track of who is paying you.</source>
        <translation>Това са входящите адреси (за получаване на плащания).  За прегледност може да предоставяте различен адрес за всяко плащане.</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="36"/>
        <source>Double-click to edit address or label</source>
        <translation>Двоен клик за редакция на адрес или име</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="63"/>
        <source>Create a new address</source>
        <translation>Създава нов адрес</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="77"/>
        <source>Copy the currently selected address to the system clipboard</source>
        <translation>Копира избрания адрес</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="66"/>
        <source>&amp;New Address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="80"/>
        <source>&amp;Copy Address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="91"/>
        <source>Show &amp;QR Code</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="102"/>
        <source>Sign a message to prove you own this address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="105"/>
        <source>&amp;Sign Message</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="116"/>
        <source>Delete the currently selected address from the list. Only sending addresses can be deleted.</source>
        <translation>Изтрива избрания адрес. Не могат да се изтриват входящи адреси.</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="119"/>
        <source>&amp;Delete</source>
        <translation>&amp;Изтрий</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="63"/>
        <source>Copy &amp;Label</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="65"/>
        <source>&amp;Edit</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="292"/>
        <source>Export Address Book Data</source>
        <translation>Запазване на адреси</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="293"/>
        <source>Comma separated file (*.csv)</source>
        <translation>CSV файл (*.csv)</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="306"/>
        <source>Error exporting</source>
        <translation>Грешка при записа</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="306"/>
        <source>Could not write to file %1.</source>
        <translation>Неуспешен запис в %1.</translation>
    </message>
</context>
<context>
    <name>AddressTableModel</name>
    <message>
        <location filename="../addresstablemodel.cpp" line="142"/>
        <source>Label</source>
        <translation>Име</translation>
    </message>
    <message>
        <location filename="../addresstablemodel.cpp" line="142"/>
        <source>Address</source>
        <translation>Адрес</translation>
    </message>
    <message>
        <location filename="../addresstablemodel.cpp" line="178"/>
        <source>(no label)</source>
        <translation>(без име)</translation>
    </message>
</context>
<context>
    <name>AskPassphraseDialog</name>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="26"/>
        <source>Passphrase Dialog</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="47"/>
        <source>Enter passphrase</source>
        <translation>Парола</translation>
    </message>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="61"/>
        <source>New passphrase</source>
        <translation>Нова парола</translation>
    </message>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="75"/>
        <source>Repeat new passphrase</source>
        <translation>Още веднъж</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="33"/>
        <source>Enter the new passphrase to the wallet.&lt;br/&gt;Please use a passphrase of &lt;b&gt;10 or more random characters&lt;/b&gt;, or &lt;b&gt;eight or more words&lt;/b&gt;.</source>
        <translation>Въведете нова парола за портфейла.&lt;br/&gt;Моля използвайте &lt;b&gt;поне 10 случайни символа&lt;/b&gt;, или &lt;b&gt;8 или повече думи&lt;/b&gt;.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="34"/>
        <source>Encrypt wallet</source>
        <translation>Криптиране на портфейла</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="37"/>
        <source>This operation needs your wallet passphrase to unlock the wallet.</source>
        <translation>Тази операция изисква Вашата парола за отключване на портфейла.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="42"/>
        <source>Unlock wallet</source>
        <translation>Отключване на портфейла</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="45"/>
        <source>This operation needs your wallet passphrase to decrypt the wallet.</source>
        <translation>Тази операция изисква Вашата парола за декриптиране на портфейла.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="50"/>
        <source>Decrypt wallet</source>
        <translation>Декриптиране на портфейла</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="53"/>
        <source>Change passphrase</source>
        <translation>Промяна на парола</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="54"/>
        <source>Enter the old and new passphrase to the wallet.</source>
        <translation>Въведете текущата и новата парола за портфейла.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="100"/>
        <source>Confirm wallet encryption</source>
        <translation>Потвърждаване на криптирането</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="101"/>
        <source>WARNING: If you encrypt your wallet and lose your passphrase, you will &lt;b&gt;LOSE ALL OF YOUR BITCOINS&lt;/b&gt;!
Are you sure you wish to encrypt your wallet?</source>
        <translation>ВНИМАНИЕ: Ако криптирате портфейла и забравите паролата &lt;b&gt;ЩЕ ЗАГУБИТЕ ВСИЧКИ КОИНИ&lt;/b&gt;!
Сигурни ли сте, че искате да криптирате портфейла?</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="110"/>
        <location filename="../askpassphrasedialog.cpp" line="159"/>
        <source>Wallet encrypted</source>
        <translation>Портфейлът е криптиран</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="111"/>
        <source>Bitcoin will close now to finish the encryption process. Remember that encrypting your wallet cannot fully protect your bitcoins from being stolen by malware infecting your computer.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="207"/>
        <location filename="../askpassphrasedialog.cpp" line="231"/>
        <source>Warning: The Caps Lock key is on.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="116"/>
        <location filename="../askpassphrasedialog.cpp" line="123"/>
        <location filename="../askpassphrasedialog.cpp" line="165"/>
        <location filename="../askpassphrasedialog.cpp" line="171"/>
        <source>Wallet encryption failed</source>
        <translation>Криптирането беше неуспешно</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="117"/>
        <source>Wallet encryption failed due to an internal error. Your wallet was not encrypted.</source>
        <translation>Криптирането на портфейла беше неуспешно поради неизвестен проблем. Портфейлът не е криптиран.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="124"/>
        <location filename="../askpassphrasedialog.cpp" line="172"/>
        <source>The supplied passphrases do not match.</source>
        <translation>Паролите не съвпадат</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="135"/>
        <source>Wallet unlock failed</source>
        <translation>Отключването беше неуспешно</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="136"/>
        <location filename="../askpassphrasedialog.cpp" line="147"/>
        <location filename="../askpassphrasedialog.cpp" line="166"/>
        <source>The passphrase entered for the wallet decryption was incorrect.</source>
        <translation>Паролата въведена за декриптиране на портфейла е грешна.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="146"/>
        <source>Wallet decryption failed</source>
        <translation>Декриптирането беше неуспешно</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="160"/>
        <source>Wallet passphrase was succesfully changed.</source>
        <translation>Паролата за портфейла беше променена успешно.</translation>
    </message>
</context>
<context>
    <name>BitcoinGUI</name>
    <message>
        <location filename="../bitcoingui.cpp" line="73"/>
        <source>Bitcoin Wallet</source>
        <translation>Биткоин портфейл</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="215"/>
        <source>Sign &amp;message...</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="248"/>
        <source>Show/Hide &amp;Bitcoin</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="515"/>
        <source>Synchronizing with network...</source>
        <translation>Синхронизиране с мрежата...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="185"/>
        <source>&amp;Overview</source>
        <translation>&amp;Баланс</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="186"/>
        <source>Show general overview of wallet</source>
        <translation>Обобщена информация за портфейла</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="191"/>
        <source>&amp;Transactions</source>
        <translation>&amp;Плащания</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="192"/>
        <source>Browse transaction history</source>
        <translation>История на входящи и изходящи плащания</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="197"/>
        <source>&amp;Address Book</source>
        <translation>&amp;Адреси</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="198"/>
        <source>Edit the list of stored addresses and labels</source>
        <translation>Редактиране на адреси</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="203"/>
        <source>&amp;Much receive</source>
        <translation>&amp;Получаване</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="204"/>
        <source>Show the list of addresses for receiving payments</source>
        <translation>Списък на адресите за получаване на плащания</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="209"/>
        <source>&amp;Send coins</source>
        <translation>&amp;Изпращане</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="216"/>
        <source>Prove you control an address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="235"/>
        <source>E&amp;xit</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="236"/>
        <source>Quit application</source>
        <translation>Затваря приложението</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="239"/>
        <source>&amp;About %1</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="240"/>
        <source>Show information about Bitcoin</source>
        <translation>Показва информация за Биткоин</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="242"/>
        <source>About &amp;Qt</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="243"/>
        <source>Show information about Qt</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="245"/>
        <source>&amp;Options...</source>
        <translation>&amp;Опции...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="252"/>
        <source>&amp;Encrypt Wallet...</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="255"/>
        <source>&amp;Backup Wallet...</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="257"/>
        <source>&amp;Change Passphrase...</source>
        <translation type="unfinished"/>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="517"/>
        <source>~%n block(s) remaining</source>
        <translation type="unfinished"><numerusform></numerusform><numerusform></numerusform></translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="528"/>
        <source>Downloaded %1 of %2 blocks of transaction history (%3% done).</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="250"/>
        <source>&amp;Export...</source>
        <translation>&amp;Запазване...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="210"/>
        <source>Send coins to a Bitcoin address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="246"/>
        <source>Modify configuration options for Bitcoin</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="249"/>
        <source>Show or hide the Bitcoin window</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="251"/>
        <source>Export the data in the current tab to a file</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="253"/>
        <source>Encrypt or decrypt wallet</source>
        <translation>Криптира или декриптира портфейла</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="256"/>
        <source>Backup wallet to another location</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="258"/>
        <source>Change the passphrase used for wallet encryption</source>
        <translation>Променя паролата за портфейла</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="259"/>
        <source>&amp;Debug window</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="260"/>
        <source>Open debugging and diagnostic console</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="261"/>
        <source>&amp;Verify message...</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="262"/>
        <source>Verify a message signature</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="286"/>
        <source>&amp;File</source>
        <translation>&amp;Файл</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="296"/>
        <source>&amp;Settings</source>
        <translation>&amp;Настройки</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="302"/>
        <source>&amp;Help</source>
        <translation>&amp;Помощ</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="311"/>
        <source>Tabs toolbar</source>
        <translation>Раздели</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="322"/>
        <source>Actions toolbar</source>
        <translation>Функции</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="334"/>
        <location filename="../bitcoingui.cpp" line="343"/>
        <source>[testnet]</source>
        <translation>[testnet]</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="343"/>
        <location filename="../bitcoingui.cpp" line="399"/>
        <source>Bitcoin client</source>
        <translation type="unfinished"/>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="492"/>
        <source>%n active connection(s) to Bitcoin network</source>
        <translation><numerusform>%n връзка към Биткоин мрежата</numerusform><numerusform>%n връзки към Биткоин мрежата</numerusform></translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="540"/>
        <source>Downloaded %1 blocks of transaction history.</source>
        <translation>%1 блока.</translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="555"/>
        <source>%n second(s) ago</source>
        <translation><numerusform>%n секунда</numerusform><numerusform>%n секунди</numerusform></translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="559"/>
        <source>%n minute(s) ago</source>
        <translation><numerusform>%n минута</numerusform><numerusform>%n минути</numerusform></translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="563"/>
        <source>%n hour(s) ago</source>
        <translation><numerusform>%n час</numerusform><numerusform>%n часа</numerusform></translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="567"/>
        <source>%n day(s) ago</source>
        <translation><numerusform>%n ден</numerusform><numerusform>%n дни</numerusform></translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="573"/>
        <source>Up to date</source>
        <translation>Синхронизиран</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="580"/>
        <source>Catching up...</source>
        <translation>Зарежда блокове...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="590"/>
        <source>Last received block was generated %1.</source>
        <translation>Последният блок е от %1.</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="649"/>
        <source>This transaction is over the size limit.  You can still send it for a fee of %1, which goes to the nodes that process your transaction and helps to support the network.  Do you want to pay the fee?</source>
        <translation>Размерът на плащането ще надвиши максималният размер на безплатно плащане.  Можете да платите срещу такса от %1, която ще бъде получена от участниците в мрежата, обработващи плащания.  Желаете ли да платите таксата?</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="654"/>
        <source>Confirm transaction fee</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="681"/>
        <source>Sent transaction</source>
        <translation>Изходящо плащане</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="682"/>
        <source>Incoming transaction</source>
        <translation>Входящо плащане</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="683"/>
        <source>Date: %1
Amount: %2
Type: %3
Address: %4
</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="804"/>
        <source>Wallet is &lt;b&gt;encrypted&lt;/b&gt; and currently &lt;b&gt;unlocked&lt;/b&gt;</source>
        <translation>Портфейлът е &lt;b&gt;криптиран&lt;/b&gt; и &lt;b&gt;отключен&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="812"/>
        <source>Wallet is &lt;b&gt;encrypted&lt;/b&gt; and currently &lt;b&gt;locked&lt;/b&gt;</source>
        <translation>Портфейлът е &lt;b&gt;криптиран&lt;/b&gt; и &lt;b&gt;заключен&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="835"/>
        <source>Backup Wallet</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="835"/>
        <source>Wallet Data (*.dat)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="838"/>
        <source>Backup Failed</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="838"/>
        <source>There was an error trying to save the wallet data to the new location.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="112"/>
        <source>A fatal error occured. Bitcoin can no longer continue safely and will quit.</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>ClientModel</name>
    <message>
        <location filename="../clientmodel.cpp" line="84"/>
        <source>Network Alert</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>DisplayOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="246"/>
        <source>Display</source>
        <translation>Показване</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="257"/>
        <source>default</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="263"/>
        <source>The user interface language can be set here. This setting will only take effect after restarting Bitcoin.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="252"/>
        <source>User Interface &amp;Language:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="273"/>
        <source>&amp;Unit to show amounts in:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="277"/>
        <source>Choose the default subdivision unit to show in the interface, and when sending coins</source>
        <translation>Изберете мерна единица</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="284"/>
        <source>&amp;Display addresses in transaction list</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="285"/>
        <source>Whether to show Bitcoin addresses in the transaction list</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="303"/>
        <source>Warning</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="303"/>
        <source>This setting will take effect after restarting Bitcoin.</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>EditAddressDialog</name>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="14"/>
        <source>Edit Address</source>
        <translation>Редактиране на адрес</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="25"/>
        <source>&amp;Label</source>
        <translation>&amp;Име</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="35"/>
        <source>The label associated with this address book entry</source>
        <translation>Името свързано с този запис в списъка с адреси</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="42"/>
        <source>&amp;Address</source>
        <translation>&amp;Адрес</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="52"/>
        <source>The address associated with this address book entry. This can only be modified for sending addresses.</source>
        <translation>Адресът свързан с този запис в списъка с адреси. Може да се променя само за изходящи адреси.</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="20"/>
        <source>New receiving address</source>
        <translation>Нов адрес за получаване</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="24"/>
        <source>New sending address</source>
        <translation>Нов адрес за изпращане</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="27"/>
        <source>Edit receiving address</source>
        <translation>Редактиране на входящ адрес</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="31"/>
        <source>Edit sending address</source>
        <translation>Редактиране на изходящ адрес</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="91"/>
        <source>The entered address &quot;%1&quot; is already in the address book.</source>
        <translation>Вече има адрес &quot;%1&quot; в списъка с адреси.</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="96"/>
        <source>The entered address &quot;%1&quot; is not a valid Bitcoin address.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="101"/>
        <source>Could not unlock wallet.</source>
        <translation>Отключването на портфейла беше неуспешно.</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="106"/>
        <source>New key generation failed.</source>
        <translation>Генерирането на ключ беше неуспешно.</translation>
    </message>
</context>
<context>
    <name>HelpMessageBox</name>
    <message>
        <location filename="../bitcoin.cpp" line="133"/>
        <location filename="../bitcoin.cpp" line="143"/>
        <source>Bitcoin-Qt</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="133"/>
        <source>version</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="135"/>
        <source>Usage:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="136"/>
        <source>options</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="138"/>
        <source>UI options</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="139"/>
        <source>Set language, for example &quot;de_DE&quot; (default: system locale)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="140"/>
        <source>Start minimized</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="141"/>
        <source>Show splash screen on startup (default: 1)</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>MainOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="227"/>
        <source>Detach block and address databases at shutdown. This means they can be moved to another data directory, but it slows down shutdown. The wallet is always detached.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="212"/>
        <source>Pay transaction &amp;fee</source>
        <translation>&amp;Такса за изходящо плащане</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="204"/>
        <source>Main</source>
        <translation>Общи</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="206"/>
        <source>Optional transaction fee per kB that helps make sure your transactions are processed quickly. Most transactions are 1 kB. Fee 0.01 recommended.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="222"/>
        <source>&amp;Start Bitcoin on system login</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="223"/>
        <source>Automatically start Bitcoin after logging in to the system</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="226"/>
        <source>&amp;Detach databases at shutdown</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>MessagePage</name>
    <message>
        <location filename="../forms/messagepage.ui" line="14"/>
        <source>Sign Message</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="20"/>
        <source>You can sign messages with your addresses to prove you own them. Be careful not to sign anything vague, as phishing attacks may try to trick you into signing your identity over to them. Only sign fully-detailed statements you agree to.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="38"/>
        <source>The address to sign the message with  (e.g. **********************************)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="48"/>
        <source>Choose adress from address book</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="58"/>
        <source>Alt+A</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="71"/>
        <source>Paste address from clipboard</source>
        <translation>Вмъкни от клипборда</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="81"/>
        <source>Alt+P</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="93"/>
        <source>Enter the message you want to sign here</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="128"/>
        <source>Copy the current signature to the system clipboard</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="131"/>
        <source>&amp;Copy Signature</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="142"/>
        <source>Reset all sign message fields</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="145"/>
        <source>Clear &amp;All</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="31"/>
        <source>Click &quot;Sign Message&quot; to get signature</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="114"/>
        <source>Sign a message to prove you own this address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="117"/>
        <source>&amp;Sign Message</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="30"/>
        <source>Enter a Bitcoin address (e.g. **********************************)</source>
        <translation>Въведете Биткоин адрес (например **********************************)</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="83"/>
        <location filename="../messagepage.cpp" line="90"/>
        <location filename="../messagepage.cpp" line="105"/>
        <location filename="../messagepage.cpp" line="117"/>
        <source>Error signing</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="83"/>
        <source>%1 is not a valid address.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="90"/>
        <source>%1 does not refer to a key.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="105"/>
        <source>Private key for %1 is not available.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="117"/>
        <source>Sign failed</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>NetworkOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="345"/>
        <source>Network</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="347"/>
        <source>Map port using &amp;UPnP</source>
        <translation>Отваряне на входящия порт чрез &amp;UPnP</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="348"/>
        <source>Automatically open the Bitcoin client port on the router. This only works when your router supports UPnP and it is enabled.</source>
        <translation>Автоматично отваряне на входящия Bitcoin порт. Работи само с рутери поддържащи UPnP.</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="351"/>
        <source>&amp;Connect through SOCKS4 proxy:</source>
        <translation>&amp;Използвай SOCKS4 прокси:</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="352"/>
        <source>Connect to the Bitcon network through a SOCKS4 proxy (e.g. when connecting through Tor)</source>
        <translation>Свързване с Биткоин мрежата чрез SOCKS4 прокси сървър (например за да анонимизирате достъпа си чрез Тор)</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="357"/>
        <source>Proxy &amp;IP:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="366"/>
        <source>&amp;Port:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="363"/>
        <source>IP address of the proxy (e.g. 127.0.0.1)</source>
        <translation>IP адрес на прокси сървъра (например 127.0.0.1)</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="372"/>
        <source>Port of the proxy (e.g. 1234)</source>
        <translation>Порт на прокси сървъра (например 1234)</translation>
    </message>
</context>
<context>
    <name>OptionsDialog</name>
    <message>
        <location filename="../optionsdialog.cpp" line="135"/>
        <source>Options</source>
        <translation>Опции</translation>
    </message>
</context>
<context>
    <name>OverviewPage</name>
    <message>
        <location filename="../forms/overviewpage.ui" line="14"/>
        <source>Form</source>
        <translation>Форма</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="47"/>
        <location filename="../forms/overviewpage.ui" line="204"/>
        <source>The displayed information may be out of date. Your wallet automatically synchronizes with the Bitcoin network after a connection is established, but this process has not completed yet.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="89"/>
        <source>Balance:</source>
        <translation>Баланс:</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="147"/>
        <source>Number of transactions:</source>
        <translation>Брой плащания:</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="118"/>
        <source>Unconfirmed:</source>
        <translation>Непотвърдени:</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="40"/>
        <source>Wallet</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="197"/>
        <source>&lt;b&gt;Recent transactions&lt;/b&gt;</source>
        <translation>&lt;b&gt;Последни плащания&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="105"/>
        <source>Your current balance</source>
        <translation>Вашият текущ баланс</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="134"/>
        <source>Total of transactions that have yet to be confirmed, and do not yet count toward the current balance</source>
        <translation>Сборът на все още непотвърдените плащания, които не са част от текущия баланс</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="154"/>
        <source>Total number of transactions in wallet</source>
        <translation>Общ брой плащания в портфейла</translation>
    </message>
    <message>
        <location filename="../overviewpage.cpp" line="110"/>
        <location filename="../overviewpage.cpp" line="111"/>
        <source>out of sync</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>QRCodeDialog</name>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="14"/>
        <source>QR Code Dialog</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="32"/>
        <source>QR Code</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="55"/>
        <source>Request Payment</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="70"/>
        <source>Amount:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="105"/>
        <source>BTC</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="121"/>
        <source>Label:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="144"/>
        <source>Message:</source>
        <translation>Съобщение:</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="186"/>
        <source>&amp;Save As...</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="45"/>
        <source>Error encoding URI into QR Code.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="63"/>
        <source>Resulting URI too long, try to reduce the text for label / message.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="120"/>
        <source>Save QR Code</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="120"/>
        <source>PNG Images (*.png)</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>RPCConsole</name>
    <message>
        <location filename="../forms/rpcconsole.ui" line="14"/>
        <source>Bitcoin debug window</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="46"/>
        <source>Client name</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="56"/>
        <location filename="../forms/rpcconsole.ui" line="79"/>
        <location filename="../forms/rpcconsole.ui" line="102"/>
        <location filename="../forms/rpcconsole.ui" line="125"/>
        <location filename="../forms/rpcconsole.ui" line="161"/>
        <location filename="../forms/rpcconsole.ui" line="214"/>
        <location filename="../forms/rpcconsole.ui" line="237"/>
        <location filename="../forms/rpcconsole.ui" line="260"/>
        <location filename="../rpcconsole.cpp" line="245"/>
        <source>N/A</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="69"/>
        <source>Client version</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="24"/>
        <source>&amp;Information</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="39"/>
        <source>Client</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="115"/>
        <source>Startup time</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="144"/>
        <source>Network</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="151"/>
        <source>Number of connections</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="174"/>
        <source>On testnet</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="197"/>
        <source>Block chain</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="204"/>
        <source>Current number of blocks</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="227"/>
        <source>Estimated total blocks</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="250"/>
        <source>Last block time</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="292"/>
        <source>Debug logfile</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="299"/>
        <source>Open the Bitcoin debug logfile from the current data directory. This can take a few seconds for large logfiles.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="302"/>
        <source>&amp;Open</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="323"/>
        <source>&amp;Console</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="92"/>
        <source>Build date</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="372"/>
        <source>Clear console</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../rpcconsole.cpp" line="212"/>
        <source>Welcome to the Bitcoin RPC console.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../rpcconsole.cpp" line="213"/>
        <source>Use up and down arrows to navigate history, and &lt;b&gt;Ctrl-L&lt;/b&gt; to clear screen.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../rpcconsole.cpp" line="214"/>
        <source>Type &lt;b&gt;help&lt;/b&gt; for an overview of available commands.</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>SendCoinsDialog</name>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="14"/>
        <location filename="../sendcoinsdialog.cpp" line="122"/>
        <location filename="../sendcoinsdialog.cpp" line="127"/>
        <location filename="../sendcoinsdialog.cpp" line="132"/>
        <location filename="../sendcoinsdialog.cpp" line="137"/>
        <location filename="../sendcoinsdialog.cpp" line="143"/>
        <location filename="../sendcoinsdialog.cpp" line="148"/>
        <location filename="../sendcoinsdialog.cpp" line="153"/>
        <source>Send Coins</source>
        <translation>Изпращане</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="64"/>
        <source>Send to multiple recipients at once</source>
        <translation>Изпращане към повече от един получател</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="67"/>
        <source>&amp;Add Recipient</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="84"/>
        <source>Remove all transaction fields</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="87"/>
        <source>Clear &amp;All</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="106"/>
        <source>Balance:</source>
        <translation>Баланс:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="113"/>
        <source>123.456 BTC</source>
        <translation>123.456 BTC</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="144"/>
        <source>Confirm the send action</source>
        <translation>Потвърдете изпращането</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="147"/>
        <source>&amp;Send</source>
        <translation>&amp;Изпращане</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="94"/>
        <source>&lt;b&gt;%1&lt;/b&gt; to %2 (%3)</source>
        <translation>&lt;b&gt;%1&lt;/b&gt; на %2 (%3)</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="99"/>
        <source>Confirm send coins</source>
        <translation>Потвърждаване</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="100"/>
        <source>Are you sure you want to send %1?</source>
        <translation>Сигурни ли сте, че искате да изпратите %1?</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="100"/>
        <source> and </source>
        <translation> и </translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="123"/>
        <source>The recepient address is not valid, please recheck.</source>
        <translation>Адресът на получателя не е валиден, моля проверете пак.</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="128"/>
        <source>The amount to pay must be larger than 0.</source>
        <translation>Сумата трябва да е по-голяма от 0.</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="133"/>
        <source>The amount exceeds your balance.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="138"/>
        <source>The total exceeds your balance when the %1 transaction fee is included.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="144"/>
        <source>Duplicate address found, can only send to each address once per send operation.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="149"/>
        <source>Error: Transaction creation failed.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="154"/>
        <source>Error: The transaction was rejected. This might happen if some of the coins in your wallet were already spent, such as if you used a copy of wallet.dat and coins were spent in the copy but not marked as spent here.</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>SendCoinsEntry</name>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="14"/>
        <source>Form</source>
        <translation>Форма</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="29"/>
        <source>A&amp;mount:</source>
        <translation>С&amp;ума:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="42"/>
        <source>Pay &amp;To:</source>
        <translation>Плати &amp;На:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="66"/>
        <location filename="../sendcoinsentry.cpp" line="25"/>
        <source>Enter a label for this address to add it to your address book</source>
        <translation>Въведете име за този адрес, за да го добавите в списъка с адреси</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="75"/>
        <source>&amp;Label:</source>
        <translation>&amp;Име:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="93"/>
        <source>The address to send the payment to  (e.g. **********************************)</source>
        <translation>Адресът, към който да се направи плащането  (например **********************************)</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="103"/>
        <source>Choose address from address book</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="113"/>
        <source>Alt+A</source>
        <translation>Alt+A</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="120"/>
        <source>Paste address from clipboard</source>
        <translation>Вмъкни от клипборда</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="130"/>
        <source>Alt+P</source>
        <translation>Alt+P</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="137"/>
        <source>Remove this recipient</source>
        <translation>Махни този получател</translation>
    </message>
    <message>
        <location filename="../sendcoinsentry.cpp" line="26"/>
        <source>Enter a Bitcoin address (e.g. **********************************)</source>
        <translation>Въведете Биткоин адрес (например **********************************)</translation>
    </message>
</context>
<context>
    <name>TransactionDesc</name>
    <message>
        <location filename="../transactiondesc.cpp" line="21"/>
        <source>Open for %1 blocks</source>
        <translation>Подлежи на промяна за %1 блока</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="23"/>
        <source>Open until %1</source>
        <translation>Подлежи на промяна до %1</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="29"/>
        <source>%1/offline?</source>
        <translation>%1/офлайн?</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="31"/>
        <source>%1/unconfirmed</source>
        <translation>%1/непотвърдено</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="33"/>
        <source>%1 confirmations</source>
        <translation>включено в %1 блока</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="51"/>
        <source>&lt;b&gt;Status:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Състояние:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="56"/>
        <source>, has not been successfully broadcast yet</source>
        <translation>, все още не е изпратено</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="58"/>
        <source>, broadcast through %1 node</source>
        <translation>, изпратено през %1 участник в мрежата</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="60"/>
        <source>, broadcast through %1 nodes</source>
        <translation>, изпратено през %1 участници в мрежата</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="64"/>
        <source>&lt;b&gt;Date:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Дата:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="71"/>
        <source>&lt;b&gt;Source:&lt;/b&gt; Generated&lt;br&gt;</source>
        <translation>&lt;b&gt;Източник:&lt;/b&gt; Генерирани&lt;br&gt;</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="77"/>
        <location filename="../transactiondesc.cpp" line="94"/>
        <source>&lt;b&gt;From:&lt;/b&gt; </source>
        <translation>&lt;b&gt;От:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="94"/>
        <source>unknown</source>
        <translation>неизвестен</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="95"/>
        <location filename="../transactiondesc.cpp" line="118"/>
        <location filename="../transactiondesc.cpp" line="178"/>
        <source>&lt;b&gt;To:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Към:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="98"/>
        <source> (yours, label: </source>
        <translation> (собствен, име: </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="100"/>
        <source> (yours)</source>
        <translation> (собствен)</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="136"/>
        <location filename="../transactiondesc.cpp" line="150"/>
        <location filename="../transactiondesc.cpp" line="195"/>
        <location filename="../transactiondesc.cpp" line="212"/>
        <source>&lt;b&gt;Credit:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Кредит:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="138"/>
        <source>(%1 matures in %2 more blocks)</source>
        <translation>(%1 достъпни след %2 блока)</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="142"/>
        <source>(not accepted)</source>
        <translation>(отхвърлен от мрежата)</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="186"/>
        <location filename="../transactiondesc.cpp" line="194"/>
        <location filename="../transactiondesc.cpp" line="209"/>
        <source>&lt;b&gt;Debit:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Дебит:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="200"/>
        <source>&lt;b&gt;Transaction fee:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Такса:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="216"/>
        <source>&lt;b&gt;Net amount:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Сума нето:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="222"/>
        <source>Message:</source>
        <translation>Съобщение:</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="224"/>
        <source>Comment:</source>
        <translation>Коментар:</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="226"/>
        <source>Transaction ID:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="229"/>
        <source>Generated coins must wait 120 blocks before they can be spent.  When you generated this block, it was broadcast to the network to be added to the block chain.  If it fails to get into the chain, it will change to &quot;not accepted&quot; and not be spendable.  This may occasionally happen if another node generates a block within a few seconds of yours.</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>TransactionDescDialog</name>
    <message>
        <location filename="../forms/transactiondescdialog.ui" line="14"/>
        <source>Transaction details</source>
        <translation>Подробности за плащане</translation>
    </message>
    <message>
        <location filename="../forms/transactiondescdialog.ui" line="20"/>
        <source>This pane shows a detailed description of the transaction</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>TransactionTableModel</name>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Date</source>
        <translation>Дата</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Type</source>
        <translation>Тип</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Address</source>
        <translation>Адрес</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Amount</source>
        <translation>Сума</translation>
    </message>
    <message numerus="yes">
        <location filename="../transactiontablemodel.cpp" line="281"/>
        <source>Open for %n block(s)</source>
        <translation type="unfinished"><numerusform></numerusform><numerusform></numerusform></translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="284"/>
        <source>Open until %1</source>
        <translation>Подлежи на промяна до %1</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="287"/>
        <source>Offline (%1 confirmations)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="290"/>
        <source>Unconfirmed (%1 of %2 confirmations)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="293"/>
        <source>Confirmed (%1 confirmations)</source>
        <translation type="unfinished"/>
    </message>
    <message numerus="yes">
        <location filename="../transactiontablemodel.cpp" line="301"/>
        <source>Mined balance will be available in %n more blocks</source>
        <translation type="unfinished"><numerusform></numerusform><numerusform></numerusform></translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="307"/>
        <source>This block was not received by any other nodes and will probably not be accepted!</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="310"/>
        <source>Generated but not accepted</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="353"/>
        <source>Received with</source>
        <translation>Получаване</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="355"/>
        <source>Received from</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="358"/>
        <source>Sent to</source>
        <translation>Изпращане</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="360"/>
        <source>Payment to yourself</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="362"/>
        <source>Mined</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="400"/>
        <source>(n/a)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="599"/>
        <source>Transaction status. Hover over this field to show number of confirmations.</source>
        <translation>Състояние на плащането. Задръжте върху това поле за брой потвърждения.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="601"/>
        <source>Date and time that the transaction was received.</source>
        <translation>Дата и час на получаване.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="603"/>
        <source>Type of transaction.</source>
        <translation>Тип плащане.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="605"/>
        <source>Destination address of transaction.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="607"/>
        <source>Amount removed from or added to balance.</source>
        <translation>Сума извадена или добавена към баланса.</translation>
    </message>
</context>
<context>
    <name>TransactionView</name>
    <message>
        <location filename="../transactionview.cpp" line="55"/>
        <location filename="../transactionview.cpp" line="71"/>
        <source>All</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="56"/>
        <source>Today</source>
        <translation>Днес</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="57"/>
        <source>This week</source>
        <translation>Тази седмица</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="58"/>
        <source>This month</source>
        <translation>Този месец</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="59"/>
        <source>Last month</source>
        <translation>Предния месец</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="60"/>
        <source>This year</source>
        <translation>Тази година</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="61"/>
        <source>Range...</source>
        <translation>Интервал...</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="72"/>
        <source>Received with</source>
        <translation>Получаване</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="74"/>
        <source>Sent to</source>
        <translation>Изпращане</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="76"/>
        <source>To yourself</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="77"/>
        <source>Mined</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="78"/>
        <source>Other</source>
        <translation>Други</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="85"/>
        <source>Enter address or label to search</source>
        <translation>Търсене по адрес или име</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="92"/>
        <source>Min amount</source>
        <translation>Минимална сума</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="126"/>
        <source>Copy address</source>
        <translation>Копирай адрес</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="127"/>
        <source>Copy label</source>
        <translation>Копирай име</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="128"/>
        <source>Copy amount</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="129"/>
        <source>Edit label</source>
        <translation>Редактирай име</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="130"/>
        <source>Show transaction details</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="270"/>
        <source>Export Transaction Data</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="271"/>
        <source>Comma separated file (*.csv)</source>
        <translation>CSV файл (*.csv)</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="279"/>
        <source>Confirmed</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="280"/>
        <source>Date</source>
        <translation>Дата</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="281"/>
        <source>Type</source>
        <translation>Тип</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="282"/>
        <source>Label</source>
        <translation>Име</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="283"/>
        <source>Address</source>
        <translation>Адрес</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="284"/>
        <source>Amount</source>
        <translation>Сума</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="285"/>
        <source>ID</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="289"/>
        <source>Error exporting</source>
        <translation>Грешка при записа</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="289"/>
        <source>Could not write to file %1.</source>
        <translation>Неуспешен запис в %1.</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="384"/>
        <source>Range:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="392"/>
        <source>to</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>VerifyMessageDialog</name>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="14"/>
        <source>Verify Signed Message</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="20"/>
        <source>Enter the message and signature below (be careful to correctly copy newlines, spaces, tabs and other invisible characters) to obtain the Bitcoin address used to sign the message.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="62"/>
        <source>Verify a message and obtain the Bitcoin address used to sign the message</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="65"/>
        <source>&amp;Verify Message</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="79"/>
        <source>Copy the currently selected address to the system clipboard</source>
        <translation>Копира избрания адрес</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="82"/>
        <source>&amp;Copy Address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="93"/>
        <source>Reset all verify message fields</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="96"/>
        <source>Clear &amp;All</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="28"/>
        <source>Enter Bitcoin signature</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="29"/>
        <source>Click &quot;Verify Message&quot; to obtain address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="55"/>
        <location filename="../verifymessagedialog.cpp" line="62"/>
        <source>Invalid Signature</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="55"/>
        <source>The signature could not be decoded. Please check the signature and try again.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="62"/>
        <source>The signature did not match the message digest. Please check the signature and try again.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="72"/>
        <source>Address not found in address book.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="72"/>
        <source>Address found in address book: %1</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>WalletModel</name>
    <message>
        <location filename="../walletmodel.cpp" line="158"/>
        <source>Sending...</source>
        <translation>Изпращане...</translation>
    </message>
</context>
<context>
    <name>WindowOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="313"/>
        <source>Window</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="316"/>
        <source>&amp;Minimize to the tray instead of the taskbar</source>
        <translation>&amp;Минимизиране в системния трей</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="317"/>
        <source>Show only a tray icon after minimizing the window</source>
        <translation>Остава видима само иконата долу вдясно</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="320"/>
        <source>M&amp;inimize on close</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="321"/>
        <source>Minimize instead of exit the application when the window is closed. When this option is enabled, the application will be closed only after selecting Quit in the menu.</source>
        <translation>При затваряне на прозореца приложението остава минимизирано. Ако изберете тази опция, приложението може да се затвори само чрез Изход в менюто.</translation>
    </message>
</context>
<context>
    <name>bitcoin-core</name>
    <message>
        <location filename="../bitcoinstrings.cpp" line="43"/>
        <source>Bitcoin version</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="44"/>
        <source>Usage:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="45"/>
        <source>Send command to -server or bitcoind</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="46"/>
        <source>List commands</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="47"/>
        <source>Get help for a command</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="49"/>
        <source>Options:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="50"/>
        <source>Specify configuration file (default: bitcoin.conf)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="51"/>
        <source>Specify pid file (default: bitcoind.pid)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="52"/>
        <source>Generate coins</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="53"/>
        <source>Don&apos;t generate coins</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="54"/>
        <source>Specify data directory</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="55"/>
        <source>Set database cache size in megabytes (default: 25)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="56"/>
        <source>Set database disk log size in megabytes (default: 100)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="57"/>
        <source>Specify connection timeout (in milliseconds)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="63"/>
        <source>Listen for connections on &lt;port&gt; (default: 8333 or testnet: 18333)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="64"/>
        <source>Maintain at most &lt;n&gt; connections to peers (default: 125)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="66"/>
        <source>Connect only to the specified node</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="67"/>
        <source>Connect to a node to retrieve peer addresses, and disconnect</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="68"/>
        <source>Specify your own public address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="69"/>
        <source>Only connect to nodes in network &lt;net&gt; (IPv4 or IPv6)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="70"/>
        <source>Try to discover public IP address (default: 1)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="73"/>
        <source>Bind to given address. Use [host]:port notation for IPv6</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="75"/>
        <source>Threshold for disconnecting misbehaving peers (default: 100)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="76"/>
        <source>Number of seconds to keep misbehaving peers from reconnecting (default: 86400)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="79"/>
        <source>Maximum per-connection receive buffer, &lt;n&gt;*1000 bytes (default: 10000)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="80"/>
        <source>Maximum per-connection send buffer, &lt;n&gt;*1000 bytes (default: 10000)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="83"/>
        <source>Detach block and address databases. Increases shutdown time (default: 0)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="86"/>
        <source>Accept command line and JSON-RPC commands</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="87"/>
        <source>Run in the background as a daemon and accept commands</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="88"/>
        <source>Use the test network</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="89"/>
        <source>Output extra debugging information</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="90"/>
        <source>Prepend debug output with timestamp</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="91"/>
        <source>Send trace/debug info to console instead of debug.log file</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="92"/>
        <source>Send trace/debug info to debugger</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="93"/>
        <source>Username for JSON-RPC connections</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="94"/>
        <source>Password for JSON-RPC connections</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="95"/>
        <source>Listen for JSON-RPC connections on &lt;port&gt; (default: 8332)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="96"/>
        <source>Allow JSON-RPC connections from specified IP address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="97"/>
        <source>Send commands to node running on &lt;ip&gt; (default: 127.0.0.1)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="98"/>
        <source>Execute command when the best block changes (%s in cmd is replaced by block hash)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="101"/>
        <source>Upgrade wallet to latest format</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="102"/>
        <source>Set key pool size to &lt;n&gt; (default: 100)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="103"/>
        <source>Rescan the block chain for missing wallet transactions</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="104"/>
        <source>How many blocks to check at startup (default: 2500, 0 = all)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="105"/>
        <source>How thorough the block verification is (0-6, default: 1)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="106"/>
        <source>Imports blocks from external blk000?.dat file</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="108"/>
        <source>
SSL options: (see the Bitcoin Wiki for SSL setup instructions)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="111"/>
        <source>Use OpenSSL (https) for JSON-RPC connections</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="112"/>
        <source>Server certificate file (default: server.cert)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="113"/>
        <source>Server private key (default: server.pem)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="114"/>
        <source>Acceptable ciphers (default: TLSv1+HIGH:!SSLv2:!aNULL:!eNULL:!AH:!3DES:@STRENGTH)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="145"/>
        <source>Warning: Disk space is low</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="107"/>
        <source>This help message</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="121"/>
        <source>Cannot obtain a lock on data directory %s.  Bitcoin is probably already running.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="48"/>
        <source>Bitcoin</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="30"/>
        <source>Unable to bind to %s on this computer (bind returned error %d, %s)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="58"/>
        <source>Connect through socks proxy</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="59"/>
        <source>Select the version of socks proxy to use (4 or 5, 5 is default)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="60"/>
        <source>Do not use proxy for connections to network &lt;net&gt; (IPv4 or IPv6)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="61"/>
        <source>Allow DNS lookups for -addnode, -seednode and -connect</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="62"/>
        <source>Pass DNS requests to (SOCKS5) proxy</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="142"/>
        <source>Loading addresses...</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="132"/>
        <source>Error loading blkindex.dat</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="134"/>
        <source>Error loading wallet.dat: Wallet corrupted</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="135"/>
        <source>Error loading wallet.dat: Wallet requires newer version of Bitcoin</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="136"/>
        <source>Wallet needed to be rewritten: restart Bitcoin to complete</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="137"/>
        <source>Error loading wallet.dat</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="124"/>
        <source>Invalid -proxy address: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="125"/>
        <source>Unknown network specified in -noproxy: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="127"/>
        <source>Unknown network specified in -onlynet: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="126"/>
        <source>Unknown -socks proxy version requested: %i</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="128"/>
        <source>Cannot resolve -bind address: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="129"/>
        <source>Not listening on any port</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="130"/>
        <source>Cannot resolve -externalip address: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="117"/>
        <source>Invalid amount for -paytxfee=&lt;amount&gt;: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="143"/>
        <source>Error: could not start node</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="31"/>
        <source>Error: Wallet locked, unable to create transaction  </source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="32"/>
        <source>Error: This transaction requires a transaction fee of at least %s because of its amount, complexity, or use of recently received funds  </source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="35"/>
        <source>Error: Transaction creation failed  </source>
        <translation>Грешка: създаването на плащане беше неуспешно  </translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="36"/>
        <source>Sending...</source>
        <translation>Изпращане...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="37"/>
        <source>Error: The transaction was rejected.  This might happen if some of the coins in your wallet were already spent, such as if you used a copy of wallet.dat and coins were spent in the copy but not marked as spent here.</source>
        <translation>Грешка: плащането беше отхвърлено.  Това е възможно ако част от парите в портфейла са вече похарчени, например при паралелно използване на копие на wallet.dat</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="41"/>
        <source>Invalid amount</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="42"/>
        <source>Insufficient funds</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="131"/>
        <source>Loading block index...</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="65"/>
        <source>Add a node to connect to and attempt to keep the connection open</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="28"/>
        <source>Unable to bind to %s on this computer. Bitcoin is probably already running.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="71"/>
        <source>Find peers using internet relay chat (default: 0)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="72"/>
        <source>Accept connections from outside (default: 1)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="74"/>
        <source>Find peers using DNS lookup (default: 1)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="81"/>
        <source>Use Universal Plug and Play to map the listening port (default: 1)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="82"/>
        <source>Use Universal Plug and Play to map the listening port (default: 0)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="85"/>
        <source>Fee per KB to add to transactions you send</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="118"/>
        <source>Warning: -paytxfee is set very high. This is the transaction fee you will pay if you send a transaction.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="133"/>
        <source>Loading wallet...</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="138"/>
        <source>Cannot downgrade wallet</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="139"/>
        <source>Cannot initialize keypool</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="140"/>
        <source>Cannot write default address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="141"/>
        <source>Rescanning...</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="144"/>
        <source>Done loading</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="8"/>
        <source>To use the %s option</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="9"/>
        <source>%s, you must set a rpcpassword in the configuration file:
 %s
It is recommended you use the following random password:
rpcuser=bitcoinrpc
rpcpassword=%s
(you do not need to remember this password)
If the file does not exist, create it with owner-readable-only file permissions.
</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="18"/>
        <source>Error</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="19"/>
        <source>An error occured while setting up the RPC port %i for listening: %s</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="20"/>
        <source>You must set rpcpassword=&lt;password&gt; in the configuration file:
%s
If the file does not exist, create it with owner-readable-only file permissions.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="25"/>
        <source>Warning: Please check that your computer&apos;s date and time are correct.  If your clock is wrong Bitcoin will not work properly.</source>
        <translation type="unfinished"/>
    </message>
</context>
</TS>