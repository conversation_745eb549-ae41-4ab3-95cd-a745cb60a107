<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="16px"
   height="16px"
   id="svg2993"
   version="1.1"
   inkscape:version="0.48.0 r9654"
   sodipodi:docname="questionmark.svg"
   inkscape:export-filename="/store/orion/projects/bitcoin/questionmark.png"
   inkscape:export-xdpi="90"
   inkscape:export-ydpi="90">
  <defs
     id="defs2995">
    <linearGradient
       id="linearGradient3808">
      <stop
         style="stop-color:#c8c8c8;stop-opacity:1;"
         offset="0"
         id="stop3810" />
      <stop
         style="stop-color:#959595;stop-opacity:1"
         offset="1"
         id="stop3812" />
    </linearGradient>
    <filter
       inkscape:collect="always"
       id="filter3804"
       x="-0.2510722"
       width="1.5021444"
       y="-0.13164773"
       height="1.2632955">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.73280473"
         id="feGaussianBlur3806" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient3808"
       id="linearGradient3844"
       x1="8.4916801"
       y1="1.4395804"
       x2="8.6022711"
       y2="14.211697"
       gradientUnits="userSpaceOnUse" />
    <filter
       inkscape:collect="always"
       id="filter3889"
       x="-0.13954329"
       width="1.2790866"
       y="-0.073168421"
       height="1.1463368">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.40728516"
         id="feGaussianBlur3891" />
    </filter>
    <filter
       inkscape:collect="always"
       id="filter3897"
       x="-0.17178624"
       width="1.3435725"
       y="-0.090074761"
       height="1.1801495">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.50139271"
         id="feGaussianBlur3899" />
    </filter>
  </defs>
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="1"
     inkscape:cx="10.789648"
     inkscape:cy="7.6382159"
     inkscape:current-layer="layer2"
     showgrid="true"
     inkscape:grid-bbox="true"
     inkscape:document-units="px"
     inkscape:window-width="1920"
     inkscape:window-height="1127"
     inkscape:window-x="0"
     inkscape:window-y="25"
     inkscape:window-maximized="1" />
  <metadata
     id="metadata2998">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:groupmode="layer"
     id="layer2"
     inkscape:label="Below"
     style="display:inline">
    <text
       sodipodi:linespacing="125%"
       id="text3006"
       y="14.748394"
       x="4.1060953"
       style="font-size:18px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans;filter:url(#filter3897)"
       xml:space="preserve"><tspan
         y="14.748394"
         x="4.1060953"
         id="tspan3008"
         sodipodi:role="line">?</tspan></text>
    <text
       xml:space="preserve"
       style="font-size:18px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans;filter:url(#filter3889)"
       x="4.1060953"
       y="14.748394"
       id="text3824"
       sodipodi:linespacing="125%"><tspan
         sodipodi:role="line"
         id="tspan3826"
         x="4.1060953"
         y="14.748394">?</tspan></text>
  </g>
  <g
     id="layer1"
     inkscape:label="QuestionMark"
     inkscape:groupmode="layer"
     style="display:inline"
     sodipodi:insensitive="true">
    <text
       xml:space="preserve"
       style="font-size:18px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:url(#linearGradient3844);fill-opacity:1;stroke:none;font-family:Sans"
       x="4.1060953"
       y="14.748394"
       id="text3001"
       sodipodi:linespacing="125%"><tspan
         sodipodi:role="line"
         id="tspan3003"
         x="4.1060953"
         y="14.748394"
         style="fill:url(#linearGradient3844);fill-opacity:1">?</tspan></text>
  </g>
</svg>
