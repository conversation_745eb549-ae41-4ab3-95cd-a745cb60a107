#!/usr/bin/env python3
"""
Build System Clues Analysis
===========================

Analyzing the build system for any hidden clues or patterns that might
relate to the private key treasure hunt.
"""

import hashlib
from ecdsa import Signing<PERSON><PERSON>, SECP256k1
from ecdsa.util import string_to_number, number_to_string

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def test_private_key(private_key_hex: str) -> bool:
    """Test if a private key generates the target public key"""
    try:
        if len(private_key_hex) != 64:
            private_key_hex = private_key_hex.zfill(64)
        
        private_key_int = int(private_key_hex, 16)
        if private_key_int == 0 or private_key_int >= SECP256k1.order:
            return False
        
        private_key_bytes = number_to_string(private_key_int, SECP256k1.order)
        signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        verifying_key = signing_key.get_verifying_key()
        
        uncompressed_pubkey = "04" + verifying_key.to_string().hex()
        return uncompressed_pubkey.lower() == TARGET_PUBKEY.lower()
    except:
        return False

def sha256_hash(data: str) -> str:
    """Generate SHA256 hash of string"""
    return hashlib.sha256(data.encode()).hexdigest()

def analyze_build_patterns():
    """Analyze patterns in the build system"""
    print("🔍 ANALYZING BUILD SYSTEM PATTERNS")
    print("=" * 50)
    
    # Key patterns from makefile
    patterns = [
        # Build target name
        "bellsd",
        "test_bells", 
        "Nintodobellsd",
        "Nintondotest_bells",
        
        # Compiler flags that might be clues
        "fstack-protector-all",
        "D_FORTIFY_SOURCE=2",
        "fPIE",
        "Wstack-protector",
        
        # Library names
        "boost_system",
        "boost_filesystem", 
        "boost_program_options",
        "boost_thread",
        "db_cxx",
        "ssl",
        "crypto",
        
        # Combined with Nintondo
        "Nintondocrypto",
        "Nintondossl",
        "Nintondodb_cxx",
        "Nintondoboost",
        
        # Build script reference
        "genbuild.sh",
        "build.h",
        "Nintondogenbuild",
        "Nintondogenbuild.sh",
        
        # Object file patterns
        "obj/version.o",
        "obj/key.o",
        "obj/main.o",
        "version.o",
        "key.o", 
        "main.o",
        "Nintondokey.o",
        "Nintondomain.o",
        "Nintondoversion.o",
    ]
    
    for pattern in patterns:
        print(f"Testing pattern: {pattern}")
        hash_result = sha256_hash(pattern)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Build pattern: {pattern}")
            return hash_result
    
    return None

def analyze_hardening_flags():
    """Analyze security hardening flags as potential clues"""
    print(f"\n🔍 ANALYZING HARDENING FLAGS")
    print("=" * 50)
    
    # The hardening section has specific flags
    hardening_elements = [
        "fno-stack-protector",
        "fstack-protector-all", 
        "Wstack-protector",
        "Wl,-z,relro",
        "Wl,-z,now",
        "fPIE",
        "pie",
        "D_FORTIFY_SOURCE=2",
        
        # Combined
        "hardening",
        "stackprotector",
        "fortify",
        "relro",
        "pie",
        
        # With Nintondo
        "Nintondohardening",
        "Nintondostackprotector", 
        "Nintondofortify",
        "Nintondorelro",
        "Nintondopie",
    ]
    
    for element in hardening_elements:
        print(f"Testing hardening element: {element}")
        hash_result = sha256_hash(element)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Hardening flag: {element}")
            return hash_result
    
    return None

def analyze_ubuntu_bug_reference():
    """Analyze the Ubuntu bug reference as a clue"""
    print(f"\n🔍 ANALYZING UBUNTU BUG REFERENCE")
    print("=" * 50)
    
    # The makefile references Ubuntu bug #691722
    bug_elements = [
        "691722",
        "ubuntu691722",
        "bug691722",
        "Nintondo691722",
        "Nintondoubuntu691722",
        "Nintondubug691722",
        
        # URL components
        "bugs.launchpad.net",
        "ubuntu",
        "gcc-4.5",
        "launchpad",
        
        # With Nintondo
        "Nintondoubuntu",
        "Nintondogcc",
        "Nintondolaunchpad",
        "Nintondogcc-4.5",
    ]
    
    for element in bug_elements:
        print(f"Testing bug reference: {element}")
        hash_result = sha256_hash(element)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Bug reference: {element}")
            return hash_result
    
    return None

def analyze_build_dependencies():
    """Analyze build dependencies as clues"""
    print(f"\n🔍 ANALYZING BUILD DEPENDENCIES")
    print("=" * 50)
    
    # Dependencies and their versions
    deps = [
        "boost",
        "openssl", 
        "berkeley-db",
        "bdb",
        "miniupnpc",
        "pthread",
        "zlib",
        
        # Version patterns
        "boost_1_53_0",
        "db-4.8.30",
        "openssl-1.0.1e",
        
        # Combined with Nintondo
        "Nintondoboost",
        "Nintondoopenssl",
        "Nintondodb",
        "Nintondopthread",
        "Nintondozlib",
        "Nintondoupnp",
        
        # Version specific
        "Nintondoboost_1_53_0",
        "Nintondodb-4.8.30",
        "Nintondoopenssl-1.0.1e",
    ]
    
    for dep in deps:
        print(f"Testing dependency: {dep}")
        hash_result = sha256_hash(dep)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Dependency: {dep}")
            return hash_result
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ BUILD SYSTEM CLUES ANALYSIS")
    print("Analyzing build system for hidden clues...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Test different analysis approaches
    result = analyze_build_patterns()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    result = analyze_hardening_flags()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    result = analyze_ubuntu_bug_reference()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    result = analyze_build_dependencies()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    print("\n💔 No treasure found in build system analysis")
    print("🔍 The build system appears to be standard without hidden keys...")

if __name__ == "__main__":
    main()
