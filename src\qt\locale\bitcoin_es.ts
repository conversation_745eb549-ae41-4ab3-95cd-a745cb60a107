<?xml version="1.0" ?><!DOCTYPE TS><TS language="es" version="2.0">
<defaultcodec>UTF-8</defaultcodec>
<context>
    <name>AboutDialog</name>
    <message>
        <location filename="../forms/aboutdialog.ui" line="14"/>
        <source>About Bitcoin</source>
        <translation>Acerca de Bitcoin</translation>
    </message>
    <message>
        <location filename="../forms/aboutdialog.ui" line="53"/>
        <source>&lt;b&gt;Bitcoin&lt;/b&gt; version</source>
        <translation>&lt;b&gt;Bitcoin&lt;/b&gt; versión</translation>
    </message>
    <message>
        <location filename="../forms/aboutdialog.ui" line="97"/>
        <source>Copyright © 2009-2012 Bitcoin Developers

This is experimental software.

Distributed under the MIT/X11 software license, see the accompanying file license.txt or http://www.opensource.org/licenses/mit-license.php.

This product includes software developed by the OpenSSL Project for use in the OpenSSL Toolkit (http://www.openssl.org/) and cryptographic software written by <PERSON> (<EMAIL>) and UPnP software written by <PERSON>.</source>
        <translation>Copyright © 2009-2012 Desarrolladores de Bitcoin ⏎
⏎
Este software es experimental. ⏎
⏎
Se distribuye bajo la licencia de software MIT/X11, consulte el archivo adjunto o license.txt http://www.opensource.org/licenses/mit-license.php. ⏎
⏎
Este producto incluye software desarrollado por el OpenSSL Project para su uso en el OpenSSL Toolkit (http://www.openssl.org/~~V) y software de criptografía escrito por Eric Young (<EMAIL>) y el software UPnP escrito por Thomas Bernard.</translation>
    </message>
</context>
<context>
    <name>AddressBookPage</name>
    <message>
        <location filename="../forms/addressbookpage.ui" line="14"/>
        <source>Address Book</source>
        <translation>Libreta de direcciones</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="20"/>
        <source>These are your Bitcoin addresses for receiving payments.  You may want to give a different one to each sender so you can keep track of who is paying you.</source>
        <translation>Éstas son sus direcciones Bitcoin para recibir pagos. Puede usar una diferente para cada persona emisora para saber quién le está pagando.</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="36"/>
        <source>Double-click to edit address or label</source>
        <translation>Haga doble clic para editar una dirección o etiqueta</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="63"/>
        <source>Create a new address</source>
        <translation>Crear una nueva dirección</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="77"/>
        <source>Copy the currently selected address to the system clipboard</source>
        <translation>Copiar la dirección seleccionada al portapapeles</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="66"/>
        <source>&amp;New Address</source>
        <translation>&amp;Añadir dirección</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="80"/>
        <source>&amp;Copy Address</source>
        <translation>&amp;Copiar dirección</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="91"/>
        <source>Show &amp;QR Code</source>
        <translation>Mostrar código &amp;QR </translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="102"/>
        <source>Sign a message to prove you own this address</source>
        <translation>Firmar un mensaje para demostrar que posee esta dirección</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="105"/>
        <source>&amp;Sign Message</source>
        <translation>&amp;Firmar mensaje</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="116"/>
        <source>Delete the currently selected address from the list. Only sending addresses can be deleted.</source>
        <translation>Borrar de la lista la dirección seleccionada . Sólo se pueden borrar las direcciones de envío.</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="119"/>
        <source>&amp;Delete</source>
        <translation>&amp;Borrar</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="63"/>
        <source>Copy &amp;Label</source>
        <translation>Copiar &amp;etiqueta</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="65"/>
        <source>&amp;Edit</source>
        <translation>&amp;Editar</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="292"/>
        <source>Export Address Book Data</source>
        <translation>Exportar datos de la libreta de direcciones</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="293"/>
        <source>Comma separated file (*.csv)</source>
        <translation>Archivos de columnas separadas por coma (*.csv)</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="306"/>
        <source>Error exporting</source>
        <translation>Error al exportar</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="306"/>
        <source>Could not write to file %1.</source>
        <translation>No se pudo escribir en el archivo %1.</translation>
    </message>
</context>
<context>
    <name>AddressTableModel</name>
    <message>
        <location filename="../addresstablemodel.cpp" line="142"/>
        <source>Label</source>
        <translation>Etiqueta</translation>
    </message>
    <message>
        <location filename="../addresstablemodel.cpp" line="142"/>
        <source>Address</source>
        <translation>Dirección</translation>
    </message>
    <message>
        <location filename="../addresstablemodel.cpp" line="178"/>
        <source>(no label)</source>
        <translation>(sin etiqueta)</translation>
    </message>
</context>
<context>
    <name>AskPassphraseDialog</name>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="26"/>
        <source>Passphrase Dialog</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="47"/>
        <source>Enter passphrase</source>
        <translation>Contraseña actual     </translation>
    </message>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="61"/>
        <source>New passphrase</source>
        <translation>Nueva contraseña</translation>
    </message>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="75"/>
        <source>Repeat new passphrase</source>
        <translation>Repita la nueva contraseña</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="33"/>
        <source>Enter the new passphrase to the wallet.&lt;br/&gt;Please use a passphrase of &lt;b&gt;10 or more random characters&lt;/b&gt;, or &lt;b&gt;eight or more words&lt;/b&gt;.</source>
        <translation>Introduzca la nueva contraseña del monedero.&lt;br/&gt;Por favor elija una con &lt;b&gt;10 o más caracteres aleatorios&lt;/b&gt; u &lt;b&gt;ocho o más palabras&lt;/b&gt;.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="34"/>
        <source>Encrypt wallet</source>
        <translation>Cifrar el monedero</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="37"/>
        <source>This operation needs your wallet passphrase to unlock the wallet.</source>
        <translation>Para desbloquear el monedero esta operación necesita de su contraseña.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="42"/>
        <source>Unlock wallet</source>
        <translation>Desbloquear monedero</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="45"/>
        <source>This operation needs your wallet passphrase to decrypt the wallet.</source>
        <translation>Para descifrar el monedero esta operación necesita de su contraseña.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="50"/>
        <source>Decrypt wallet</source>
        <translation>Descifrar monedero</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="53"/>
        <source>Change passphrase</source>
        <translation>Cambiar contraseña</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="54"/>
        <source>Enter the old and new passphrase to the wallet.</source>
        <translation>Introduzca la contraseña anterior del monedero y la nueva. </translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="100"/>
        <source>Confirm wallet encryption</source>
        <translation>Confirmar cifrado del monedero</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="101"/>
        <source>WARNING: If you encrypt your wallet and lose your passphrase, you will &lt;b&gt;LOSE ALL OF YOUR BITCOINS&lt;/b&gt;!
Are you sure you wish to encrypt your wallet?</source>
        <translation>ATENCIÓN: ¡Si cifra el monedero y pierde la contraseña perderá &lt;b&gt;TODOS SUS BITCOINS&lt;/b&gt;!&quot;
¿Está seguro de querer cifrarlo?</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="110"/>
        <location filename="../askpassphrasedialog.cpp" line="159"/>
        <source>Wallet encrypted</source>
        <translation>Monedero cifrado</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="111"/>
        <source>Bitcoin will close now to finish the encryption process. Remember that encrypting your wallet cannot fully protect your bitcoins from being stolen by malware infecting your computer.</source>
        <translation>Bitcoin cerrará al finalizar el proceso de cifrado. Recuerde que el cifrado de su monedero no puede proteger totalmente sus bitcoin de ser robados por el malware que infecte su sistema.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="207"/>
        <location filename="../askpassphrasedialog.cpp" line="231"/>
        <source>Warning: The Caps Lock key is on.</source>
        <translation>Aviso: el bloqueo de mayúsculas está activado.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="116"/>
        <location filename="../askpassphrasedialog.cpp" line="123"/>
        <location filename="../askpassphrasedialog.cpp" line="165"/>
        <location filename="../askpassphrasedialog.cpp" line="171"/>
        <source>Wallet encryption failed</source>
        <translation>Ha fallado el cifrado del monedero</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="117"/>
        <source>Wallet encryption failed due to an internal error. Your wallet was not encrypted.</source>
        <translation>Ha fallado el cifrado del monedero debido a un error interno. El monedero no ha sido cifrado.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="124"/>
        <location filename="../askpassphrasedialog.cpp" line="172"/>
        <source>The supplied passphrases do not match.</source>
        <translation>Las contraseñas no coinciden.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="135"/>
        <source>Wallet unlock failed</source>
        <translation>Ha fallado el desbloqueo del monedero</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="136"/>
        <location filename="../askpassphrasedialog.cpp" line="147"/>
        <location filename="../askpassphrasedialog.cpp" line="166"/>
        <source>The passphrase entered for the wallet decryption was incorrect.</source>
        <translation>La contraseña introducida para descifrar el monedero es incorrecta.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="146"/>
        <source>Wallet decryption failed</source>
        <translation>Ha fallado el descifrado del monedero</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="160"/>
        <source>Wallet passphrase was succesfully changed.</source>
        <translation>La contraseña del monedero ha sido cambiada correctamente.</translation>
    </message>
</context>
<context>
    <name>BitcoinGUI</name>
    <message>
        <location filename="../bitcoingui.cpp" line="73"/>
        <source>Bitcoin Wallet</source>
        <translation>Monedero Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="215"/>
        <source>Sign &amp;message...</source>
        <translation>Firmar &amp;mensaje...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="248"/>
        <source>Show/Hide &amp;Bitcoin</source>
        <translation>Mostrar/ocultar &amp;Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="515"/>
        <source>Synchronizing with network...</source>
        <translation>Sincronizando con la red…</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="185"/>
        <source>&amp;Overview</source>
        <translation>&amp;Vista general</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="186"/>
        <source>Show general overview of wallet</source>
        <translation>Mostrar vista general del monedero</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="191"/>
        <source>&amp;Transactions</source>
        <translation>&amp;Transacciones</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="192"/>
        <source>Browse transaction history</source>
        <translation>Examinar el historial de transacciones</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="197"/>
        <source>&amp;Address Book</source>
        <translation>&amp;Libreta de direcciones</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="198"/>
        <source>Edit the list of stored addresses and labels</source>
        <translation>Editar la lista de las direcciones y etiquetas almacenadas</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="203"/>
        <source>&amp;Receive coins</source>
        <translation>&amp;Recibir monedas</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="204"/>
        <source>Show the list of addresses for receiving payments</source>
        <translation>Mostrar la lista de direcciones utilizadas para recibir pagos</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="209"/>
        <source>&amp;Send coins</source>
        <translation>&amp;Enviar monedas</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="216"/>
        <source>Prove you control an address</source>
        <translation>Demuestre que controla una dirección</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="235"/>
        <source>E&amp;xit</source>
        <translation>&amp;Salir</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="236"/>
        <source>Quit application</source>
        <translation>Salir de la aplicación</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="239"/>
        <source>&amp;About %1</source>
        <translation>&amp;Acerca de %1</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="240"/>
        <source>Show information about Bitcoin</source>
        <translation>Mostrar información acerca de Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="242"/>
        <source>About &amp;Qt</source>
        <translation>Acerca de &amp;Qt</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="243"/>
        <source>Show information about Qt</source>
        <translation>Mostrar información acerca de Qt</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="245"/>
        <source>&amp;Options...</source>
        <translation>&amp;Opciones...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="252"/>
        <source>&amp;Encrypt Wallet...</source>
        <translation>&amp;Cifrar monedero…</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="255"/>
        <source>&amp;Backup Wallet...</source>
        <translation>Copia de &amp;respaldo del monedero...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="257"/>
        <source>&amp;Change Passphrase...</source>
        <translation>&amp;Cambiar la contraseña…</translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="517"/>
        <source>~%n block(s) remaining</source>
        <translation><numerusform>~%n bloque restante</numerusform><numerusform>~%n bloques restantes</numerusform></translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="528"/>
        <source>Downloaded %1 of %2 blocks of transaction history (%3% done).</source>
        <translation>Descargado %1 de %2 bloques del historial de transacciones (%3% hecho).</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="250"/>
        <source>&amp;Export...</source>
        <translation>&amp;Exportar…</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="210"/>
        <source>Send coins to a Bitcoin address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="246"/>
        <source>Modify configuration options for Bitcoin</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="249"/>
        <source>Show or hide the Bitcoin window</source>
        <translation>Mostrar u ocultar la ventana Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="251"/>
        <source>Export the data in the current tab to a file</source>
        <translation>Exportar a un archivo los datos de esta pestaña</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="253"/>
        <source>Encrypt or decrypt wallet</source>
        <translation>Cifrar o descifrar el monedero</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="256"/>
        <source>Backup wallet to another location</source>
        <translation>Copia de seguridad del monedero en otra ubicación</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="258"/>
        <source>Change the passphrase used for wallet encryption</source>
        <translation>Cambiar la contraseña utilizada para el cifrado del monedero</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="259"/>
        <source>&amp;Debug window</source>
        <translation>Ventana de &amp;depuración</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="260"/>
        <source>Open debugging and diagnostic console</source>
        <translation>Abrir la consola de depuración y diagnóstico</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="261"/>
        <source>&amp;Verify message...</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="262"/>
        <source>Verify a message signature</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="286"/>
        <source>&amp;File</source>
        <translation>&amp;Archivo</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="296"/>
        <source>&amp;Settings</source>
        <translation>&amp;Configuración</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="302"/>
        <source>&amp;Help</source>
        <translation>A&amp;yuda</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="311"/>
        <source>Tabs toolbar</source>
        <translation>Barra de pestañas</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="322"/>
        <source>Actions toolbar</source>
        <translation>Barra de acciones</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="334"/>
        <location filename="../bitcoingui.cpp" line="343"/>
        <source>[testnet]</source>
        <translation>[testnet]</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="343"/>
        <location filename="../bitcoingui.cpp" line="399"/>
        <source>Bitcoin client</source>
        <translation>cliente Bitcoin</translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="492"/>
        <source>%n active connection(s) to Bitcoin network</source>
        <translation><numerusform>%n conexión activa hacia la red Bitcoin</numerusform><numerusform>%n conexiones activas hacia la red Bitcoin</numerusform></translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="540"/>
        <source>Downloaded %1 blocks of transaction history.</source>
        <translation>Se han bajado %1 bloques de historial.</translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="555"/>
        <source>%n second(s) ago</source>
        <translation><numerusform>hace %n segundo</numerusform><numerusform>hace %n segundos</numerusform></translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="559"/>
        <source>%n minute(s) ago</source>
        <translation><numerusform>hace %n minuto</numerusform><numerusform>hace %n minutos</numerusform></translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="563"/>
        <source>%n hour(s) ago</source>
        <translation><numerusform>hace %n hora</numerusform><numerusform>hace %n horas</numerusform></translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="567"/>
        <source>%n day(s) ago</source>
        <translation><numerusform>hace %n día</numerusform><numerusform>hace %n días</numerusform></translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="573"/>
        <source>Up to date</source>
        <translation>Actualizado</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="580"/>
        <source>Catching up...</source>
        <translation>Recuperando...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="590"/>
        <source>Last received block was generated %1.</source>
        <translation>El último bloque recibido fue generado %1.</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="649"/>
        <source>This transaction is over the size limit.  You can still send it for a fee of %1, which goes to the nodes that process your transaction and helps to support the network.  Do you want to pay the fee?</source>
        <translation>Esta transacción supera el límite. Puede seguir enviándola incluyendo una comisión de %1 que se va a repartir entre los nodos que procesan su transacción y ayudan a mantener la red. ¿Desea pagar esa tarifa?</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="654"/>
        <source>Confirm transaction fee</source>
        <translation>Confirme la tarifa de la transacción</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="681"/>
        <source>Sent transaction</source>
        <translation>Transacción enviada</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="682"/>
        <source>Incoming transaction</source>
        <translation>Transacción entrante</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="683"/>
        <source>Date: %1
Amount: %2
Type: %3
Address: %4
</source>
        <translation>Fecha: %1
Cantidad: %2
Tipo: %3
Dirección: %4</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="804"/>
        <source>Wallet is &lt;b&gt;encrypted&lt;/b&gt; and currently &lt;b&gt;unlocked&lt;/b&gt;</source>
        <translation>El monedero está &lt;b&gt;cifrado&lt;/b&gt; y actualmente &lt;b&gt;desbloqueado&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="812"/>
        <source>Wallet is &lt;b&gt;encrypted&lt;/b&gt; and currently &lt;b&gt;locked&lt;/b&gt;</source>
        <translation>El monedero está &lt;b&gt;cifrado&lt;/b&gt; y actualmente &lt;b&gt;bloqueado&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="835"/>
        <source>Backup Wallet</source>
        <translation>Copia de seguridad del monedero</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="835"/>
        <source>Wallet Data (*.dat)</source>
        <translation>Datos del monedero (*.dat)</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="838"/>
        <source>Backup Failed</source>
        <translation>La copia de seguridad ha fallado</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="838"/>
        <source>There was an error trying to save the wallet data to the new location.</source>
        <translation>Ha habido un error al intentar guardar los datos del monedero a la nueva ubicación.</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="112"/>
        <source>A fatal error occured. Bitcoin can no longer continue safely and will quit.</source>
        <translation>Ha ocurrido un error fatal. Bitcoin no puede continuar con seguridad y se cerrará.</translation>
    </message>
</context>
<context>
    <name>ClientModel</name>
    <message>
        <location filename="../clientmodel.cpp" line="84"/>
        <source>Network Alert</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>DisplayOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="246"/>
        <source>Display</source>
        <translation>Mostrado</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="257"/>
        <source>default</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="263"/>
        <source>The user interface language can be set here. This setting will only take effect after restarting Bitcoin.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="252"/>
        <source>User Interface &amp;Language:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="273"/>
        <source>&amp;Unit to show amounts in:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="277"/>
        <source>Choose the default subdivision unit to show in the interface, and when sending coins</source>
        <translation>Elige la subdivisión por defecto para mostrar cantidaded en la interfaz cuando se envien monedas</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="284"/>
        <source>&amp;Display addresses in transaction list</source>
        <translation>&amp;Mostrar las direcciones en la lista de transsaciones</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="285"/>
        <source>Whether to show Bitcoin addresses in the transaction list</source>
        <translation>Mostrar las direcciones bitcoin en la lista de transacciones</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="303"/>
        <source>Warning</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="303"/>
        <source>This setting will take effect after restarting Bitcoin.</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>EditAddressDialog</name>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="14"/>
        <source>Edit Address</source>
        <translation>Editar Dirección</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="25"/>
        <source>&amp;Label</source>
        <translation>&amp;Etiqueta</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="35"/>
        <source>The label associated with this address book entry</source>
        <translation>La etiqueta asociada con esta entrada en la libreta</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="42"/>
        <source>&amp;Address</source>
        <translation>&amp;Dirección</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="52"/>
        <source>The address associated with this address book entry. This can only be modified for sending addresses.</source>
        <translation>La dirección asociada con esta entrada en la guia. Solo puede ser modificada para direcciones de envío.</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="20"/>
        <source>New receiving address</source>
        <translation>Nueva dirección para recibir</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="24"/>
        <source>New sending address</source>
        <translation>Nueva dirección para enviar</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="27"/>
        <source>Edit receiving address</source>
        <translation>Editar dirección de recepción</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="31"/>
        <source>Edit sending address</source>
        <translation>Editar dirección de envio</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="91"/>
        <source>The entered address &quot;%1&quot; is already in the address book.</source>
        <translation>La dirección introducida &quot;%1&quot; ya está presente en la libreta de direcciones.</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="96"/>
        <source>The entered address &quot;%1&quot; is not a valid Bitcoin address.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="101"/>
        <source>Could not unlock wallet.</source>
        <translation>No se pudo desbloquear el monedero.</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="106"/>
        <source>New key generation failed.</source>
        <translation>Ha fallado la generación de la nueva clave.</translation>
    </message>
</context>
<context>
    <name>HelpMessageBox</name>
    <message>
        <location filename="../bitcoin.cpp" line="133"/>
        <location filename="../bitcoin.cpp" line="143"/>
        <source>Bitcoin-Qt</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="133"/>
        <source>version</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="135"/>
        <source>Usage:</source>
        <translation>Uso:</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="136"/>
        <source>options</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="138"/>
        <source>UI options</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="139"/>
        <source>Set language, for example &quot;de_DE&quot; (default: system locale)</source>
        <translation>Establecer el idioma, por ejemplo, &quot;es_ES&quot; (por defecto: configuración regional del sistema)</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="140"/>
        <source>Start minimized</source>
        <translation>Arrancar minimizado</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="141"/>
        <source>Show splash screen on startup (default: 1)</source>
        <translation>Mostrar pantalla de bienvenida en el inicio (por defecto: 1)</translation>
    </message>
</context>
<context>
    <name>MainOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="227"/>
        <source>Detach block and address databases at shutdown. This means they can be moved to another data directory, but it slows down shutdown. The wallet is always detached.</source>
        <translation>Desconectar las bases de datos de bloques y direcciones al cerrar la aplicación. Implica que pueden moverse a otros directorios de datos pero ralentiza el cierre. El monedero siempre queda desconectado.</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="212"/>
        <source>Pay transaction &amp;fee</source>
        <translation>Comisión de &amp;transacciones</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="204"/>
        <source>Main</source>
        <translation>Principal</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="206"/>
        <source>Optional transaction fee per kB that helps make sure your transactions are processed quickly. Most transactions are 1 kB. Fee 0.01 recommended.</source>
        <translation>Tarifa de transacción por KB opcional que ayuda a asegurarse de que sus transacciones se procesan rápidamente. La mayoría de las transacciones son de 1 KB. Tarifa de 0,01 recomendado.</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="222"/>
        <source>&amp;Start Bitcoin on system login</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="223"/>
        <source>Automatically start Bitcoin after logging in to the system</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="226"/>
        <source>&amp;Detach databases at shutdown</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>MessagePage</name>
    <message>
        <location filename="../forms/messagepage.ui" line="14"/>
        <source>Sign Message</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="20"/>
        <source>You can sign messages with your addresses to prove you own them. Be careful not to sign anything vague, as phishing attacks may try to trick you into signing your identity over to them. Only sign fully-detailed statements you agree to.</source>
        <translation>Usted puede firmar los mensajes con sus direcciones para demostrar que las posee. Tenga cuidado de no firmar cualquier cosa vaga, ya que los ataques de phishing pueden tratar de engañarle firmando su identidad a través de ellos. Solo firme declaraciones totalmente detalladas con las que usted esté de acuerdo.</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="38"/>
        <source>The address to sign the message with  (e.g. **********************************)</source>
        <translation>La dirección para firmar el mensaje con  (por ejemplo, **********************************)</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="48"/>
        <source>Choose adress from address book</source>
        <translation>Elige la dirección de la libreta</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="58"/>
        <source>Alt+A</source>
        <translation>Alt+A</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="71"/>
        <source>Paste address from clipboard</source>
        <translation>Pega dirección desde portapapeles</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="81"/>
        <source>Alt+P</source>
        <translation>Alt+P</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="93"/>
        <source>Enter the message you want to sign here</source>
        <translation>Introduzca el mensaje que desea firmar aquí</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="128"/>
        <source>Copy the current signature to the system clipboard</source>
        <translation>Copiar la firma actual al portapapeles del sistema</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="131"/>
        <source>&amp;Copy Signature</source>
        <translation>&amp;Copiar firma</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="142"/>
        <source>Reset all sign message fields</source>
        <translation>Limpiar todos los campos de mensaje de la firma</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="145"/>
        <source>Clear &amp;All</source>
        <translation>Limpiar &amp;todo</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="31"/>
        <source>Click &quot;Sign Message&quot; to get signature</source>
        <translation>Haga clic en &quot;Firma Mensaje&quot; para obtener la firma</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="114"/>
        <source>Sign a message to prove you own this address</source>
        <translation>Firmar un mensaje a demostrar que posee esta dirección</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="117"/>
        <source>&amp;Sign Message</source>
        <translation>&amp;Firme mensaje</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="30"/>
        <source>Enter a Bitcoin address (e.g. **********************************)</source>
        <translation>Introduce una dirección Bitcoin (ej. **********************************)</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="83"/>
        <location filename="../messagepage.cpp" line="90"/>
        <location filename="../messagepage.cpp" line="105"/>
        <location filename="../messagepage.cpp" line="117"/>
        <source>Error signing</source>
        <translation>Error en firmado</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="83"/>
        <source>%1 is not a valid address.</source>
        <translation>%1 no es una dirección válida.</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="90"/>
        <source>%1 does not refer to a key.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="105"/>
        <source>Private key for %1 is not available.</source>
        <translation>La clave privada de %1 no está disponible.</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="117"/>
        <source>Sign failed</source>
        <translation>Firma falló</translation>
    </message>
</context>
<context>
    <name>NetworkOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="345"/>
        <source>Network</source>
        <translation>Red</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="347"/>
        <source>Map port using &amp;UPnP</source>
        <translation>Mapea el puerto usando &amp;UPnP</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="348"/>
        <source>Automatically open the Bitcoin client port on the router. This only works when your router supports UPnP and it is enabled.</source>
        <translation>Intenta abrir el puerto adecuado en el router automáticamente. Esta opción solo funciona si el router soporta UPnP y está activado.</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="351"/>
        <source>&amp;Connect through SOCKS4 proxy:</source>
        <translation>&amp;Conecta atraves de un proxy SOCKS4:</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="352"/>
        <source>Connect to the Bitcon network through a SOCKS4 proxy (e.g. when connecting through Tor)</source>
        <translation>Conecta a la red Bitcoin a través de un proxy SOCKS4 (ej. para conectar con la red Tor)</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="357"/>
        <source>Proxy &amp;IP:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="366"/>
        <source>&amp;Port:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="363"/>
        <source>IP address of the proxy (e.g. 127.0.0.1)</source>
        <translation>Dirección IP del proxy (ej. 127.0.0.1)</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="372"/>
        <source>Port of the proxy (e.g. 1234)</source>
        <translation>Puerto del servidor proxy (ej. 1234)</translation>
    </message>
</context>
<context>
    <name>OptionsDialog</name>
    <message>
        <location filename="../optionsdialog.cpp" line="135"/>
        <source>Options</source>
        <translation>Opciones</translation>
    </message>
</context>
<context>
    <name>OverviewPage</name>
    <message>
        <location filename="../forms/overviewpage.ui" line="14"/>
        <source>Form</source>
        <translation>Desde</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="47"/>
        <location filename="../forms/overviewpage.ui" line="204"/>
        <source>The displayed information may be out of date. Your wallet automatically synchronizes with the Bitcoin network after a connection is established, but this process has not completed yet.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="89"/>
        <source>Balance:</source>
        <translation>Balance:</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="147"/>
        <source>Number of transactions:</source>
        <translation>Número de movimientos:</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="118"/>
        <source>Unconfirmed:</source>
        <translation>No confirmado(s):</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="40"/>
        <source>Wallet</source>
        <translation>Monedero</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="197"/>
        <source>&lt;b&gt;Recent transactions&lt;/b&gt;</source>
        <translation>&lt;b&gt;Movimientos recientes&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="105"/>
        <source>Your current balance</source>
        <translation>Tu balance actual</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="134"/>
        <source>Total of transactions that have yet to be confirmed, and do not yet count toward the current balance</source>
        <translation>Total de las transacciones que faltan por confirmar y que no se cuentan para el total general</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="154"/>
        <source>Total number of transactions in wallet</source>
        <translation>Número total de movimientos en el monedero</translation>
    </message>
    <message>
        <location filename="../overviewpage.cpp" line="110"/>
        <location filename="../overviewpage.cpp" line="111"/>
        <source>out of sync</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>QRCodeDialog</name>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="14"/>
        <source>QR Code Dialog</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="32"/>
        <source>QR Code</source>
        <translation>Código QR</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="55"/>
        <source>Request Payment</source>
        <translation>Solicitud de pago</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="70"/>
        <source>Amount:</source>
        <translation>Cuantía:</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="105"/>
        <source>BTC</source>
        <translation>BTC</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="121"/>
        <source>Label:</source>
        <translation>Label:</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="144"/>
        <source>Message:</source>
        <translation>Mensaje:</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="186"/>
        <source>&amp;Save As...</source>
        <translation>&amp;Guardar Como ...</translation>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="45"/>
        <source>Error encoding URI into QR Code.</source>
        <translation>Error al codificar la URI en el código QR.</translation>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="63"/>
        <source>Resulting URI too long, try to reduce the text for label / message.</source>
        <translation>URI demasiado larga, trata de reducir el texto de la etiqueta / mensaje.</translation>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="120"/>
        <source>Save QR Code</source>
        <translation>Guardar código QR</translation>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="120"/>
        <source>PNG Images (*.png)</source>
        <translation>Imágenes PNG (*.png)</translation>
    </message>
</context>
<context>
    <name>RPCConsole</name>
    <message>
        <location filename="../forms/rpcconsole.ui" line="14"/>
        <source>Bitcoin debug window</source>
        <translation>Ventana de depuración</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="46"/>
        <source>Client name</source>
        <translation>Nombre del cliente</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="56"/>
        <location filename="../forms/rpcconsole.ui" line="79"/>
        <location filename="../forms/rpcconsole.ui" line="102"/>
        <location filename="../forms/rpcconsole.ui" line="125"/>
        <location filename="../forms/rpcconsole.ui" line="161"/>
        <location filename="../forms/rpcconsole.ui" line="214"/>
        <location filename="../forms/rpcconsole.ui" line="237"/>
        <location filename="../forms/rpcconsole.ui" line="260"/>
        <location filename="../rpcconsole.cpp" line="245"/>
        <source>N/A</source>
        <translation>N/D</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="69"/>
        <source>Client version</source>
        <translation>Versión del cliente</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="24"/>
        <source>&amp;Information</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="39"/>
        <source>Client</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="115"/>
        <source>Startup time</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="144"/>
        <source>Network</source>
        <translation>Red</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="151"/>
        <source>Number of connections</source>
        <translation>Número de conexiones</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="174"/>
        <source>On testnet</source>
        <translation>En la red de pruebas</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="197"/>
        <source>Block chain</source>
        <translation>Cadena de bloques</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="204"/>
        <source>Current number of blocks</source>
        <translation>Número actual de bloques</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="227"/>
        <source>Estimated total blocks</source>
        <translation>Bloques totales estimados</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="250"/>
        <source>Last block time</source>
        <translation>Hora del último bloque</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="292"/>
        <source>Debug logfile</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="299"/>
        <source>Open the Bitcoin debug logfile from the current data directory. This can take a few seconds for large logfiles.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="302"/>
        <source>&amp;Open</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="323"/>
        <source>&amp;Console</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="92"/>
        <source>Build date</source>
        <translation>Fecha de compilación</translation>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="372"/>
        <source>Clear console</source>
        <translation>Borrar consola</translation>
    </message>
    <message>
        <location filename="../rpcconsole.cpp" line="212"/>
        <source>Welcome to the Bitcoin RPC console.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../rpcconsole.cpp" line="213"/>
        <source>Use up and down arrows to navigate history, and &lt;b&gt;Ctrl-L&lt;/b&gt; to clear screen.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../rpcconsole.cpp" line="214"/>
        <source>Type &lt;b&gt;help&lt;/b&gt; for an overview of available commands.</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>SendCoinsDialog</name>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="14"/>
        <location filename="../sendcoinsdialog.cpp" line="122"/>
        <location filename="../sendcoinsdialog.cpp" line="127"/>
        <location filename="../sendcoinsdialog.cpp" line="132"/>
        <location filename="../sendcoinsdialog.cpp" line="137"/>
        <location filename="../sendcoinsdialog.cpp" line="143"/>
        <location filename="../sendcoinsdialog.cpp" line="148"/>
        <location filename="../sendcoinsdialog.cpp" line="153"/>
        <source>Send Coins</source>
        <translation>Envía monedas</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="64"/>
        <source>Send to multiple recipients at once</source>
        <translation>Envía a multiples destinatarios de una vez</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="67"/>
        <source>&amp;Add Recipient</source>
        <translation>&amp;Añadir destinatario</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="84"/>
        <source>Remove all transaction fields</source>
        <translation>Eliminar todos los campos de las transacciones</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="87"/>
        <source>Clear &amp;All</source>
        <translation>Limpiar &amp;todo</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="106"/>
        <source>Balance:</source>
        <translation>Balance:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="113"/>
        <source>123.456 BTC</source>
        <translation>123.456 BTC</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="144"/>
        <source>Confirm the send action</source>
        <translation>Confirma el envío</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="147"/>
        <source>&amp;Send</source>
        <translation>&amp;Envía</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="94"/>
        <source>&lt;b&gt;%1&lt;/b&gt; to %2 (%3)</source>
        <translation>&lt;b&gt;%1&lt;/b&gt; to %2 (%3)</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="99"/>
        <source>Confirm send coins</source>
        <translation>Confirmar el envío de monedas</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="100"/>
        <source>Are you sure you want to send %1?</source>
        <translation>Estas seguro que quieres enviar %1?</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="100"/>
        <source> and </source>
        <translation>y</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="123"/>
        <source>The recepient address is not valid, please recheck.</source>
        <translation>La dirección de destinatario no es válida, comprueba otra vez.</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="128"/>
        <source>The amount to pay must be larger than 0.</source>
        <translation>La cantidad por pagar tiene que ser mayor 0.</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="133"/>
        <source>The amount exceeds your balance.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="138"/>
        <source>The total exceeds your balance when the %1 transaction fee is included.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="144"/>
        <source>Duplicate address found, can only send to each address once per send operation.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="149"/>
        <source>Error: Transaction creation failed.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="154"/>
        <source>Error: The transaction was rejected. This might happen if some of the coins in your wallet were already spent, such as if you used a copy of wallet.dat and coins were spent in the copy but not marked as spent here.</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>SendCoinsEntry</name>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="14"/>
        <source>Form</source>
        <translation>Envio</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="29"/>
        <source>A&amp;mount:</source>
        <translation>Ca&amp;ntidad:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="42"/>
        <source>Pay &amp;To:</source>
        <translation>&amp;Pagar a:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="66"/>
        <location filename="../sendcoinsentry.cpp" line="25"/>
        <source>Enter a label for this address to add it to your address book</source>
        <translation>Etiquete esta dirección para añadirla a la libreta</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="75"/>
        <source>&amp;Label:</source>
        <translation>&amp;Etiqueta:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="93"/>
        <source>The address to send the payment to  (e.g. **********************************)</source>
        <translation>La dirección donde enviar el pago (ej. **********************************)</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="103"/>
        <source>Choose address from address book</source>
        <translation>Elija una dirección de la libreta de direcciones</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="113"/>
        <source>Alt+A</source>
        <translation>Alt+A</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="120"/>
        <source>Paste address from clipboard</source>
        <translation>Pega dirección desde portapapeles</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="130"/>
        <source>Alt+P</source>
        <translation>Alt+P</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="137"/>
        <source>Remove this recipient</source>
        <translation>Elimina destinatario</translation>
    </message>
    <message>
        <location filename="../sendcoinsentry.cpp" line="26"/>
        <source>Enter a Bitcoin address (e.g. **********************************)</source>
        <translation>Introduce una dirección Bitcoin (ej. **********************************)</translation>
    </message>
</context>
<context>
    <name>TransactionDesc</name>
    <message>
        <location filename="../transactiondesc.cpp" line="21"/>
        <source>Open for %1 blocks</source>
        <translation>Abierto hasta %1 bloques</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="23"/>
        <source>Open until %1</source>
        <translation>Abierto hasta %1</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="29"/>
        <source>%1/offline?</source>
        <translation>%1/fuera de linea?</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="31"/>
        <source>%1/unconfirmed</source>
        <translation>%1/no confirmado</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="33"/>
        <source>%1 confirmations</source>
        <translation>%1 confirmaciones</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="51"/>
        <source>&lt;b&gt;Status:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Estado:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="56"/>
        <source>, has not been successfully broadcast yet</source>
        <translation>, no ha sido emitido satisfactoriamente todavía</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="58"/>
        <source>, broadcast through %1 node</source>
        <translation>, emitido mediante %1 nodo</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="60"/>
        <source>, broadcast through %1 nodes</source>
        <translation>, emitido mediante %1 nodos</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="64"/>
        <source>&lt;b&gt;Date:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Fecha:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="71"/>
        <source>&lt;b&gt;Source:&lt;/b&gt; Generated&lt;br&gt;</source>
        <translation>&lt;b&gt;Fuente:&lt;/b&gt; Generado&lt;br&gt;</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="77"/>
        <location filename="../transactiondesc.cpp" line="94"/>
        <source>&lt;b&gt;From:&lt;/b&gt; </source>
        <translation>&lt;b&gt;De:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="94"/>
        <source>unknown</source>
        <translation>desconocido</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="95"/>
        <location filename="../transactiondesc.cpp" line="118"/>
        <location filename="../transactiondesc.cpp" line="178"/>
        <source>&lt;b&gt;To:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Para:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="98"/>
        <source> (yours, label: </source>
        <translation>(tuya, etiqueta: </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="100"/>
        <source> (yours)</source>
        <translation> (tuya)</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="136"/>
        <location filename="../transactiondesc.cpp" line="150"/>
        <location filename="../transactiondesc.cpp" line="195"/>
        <location filename="../transactiondesc.cpp" line="212"/>
        <source>&lt;b&gt;Credit:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Crédito:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="138"/>
        <source>(%1 matures in %2 more blocks)</source>
        <translation>(%1 madura en %1 bloques más)</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="142"/>
        <source>(not accepted)</source>
        <translation>(no aceptada)</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="186"/>
        <location filename="../transactiondesc.cpp" line="194"/>
        <location filename="../transactiondesc.cpp" line="209"/>
        <source>&lt;b&gt;Debit:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Débito:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="200"/>
        <source>&lt;b&gt;Transaction fee:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Comisión de transacción:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="216"/>
        <source>&lt;b&gt;Net amount:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Cantidad total:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="222"/>
        <source>Message:</source>
        <translation>Mensaje:</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="224"/>
        <source>Comment:</source>
        <translation>Comentario:</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="226"/>
        <source>Transaction ID:</source>
        <translation>Id. de transacción:</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="229"/>
        <source>Generated coins must wait 120 blocks before they can be spent.  When you generated this block, it was broadcast to the network to be added to the block chain.  If it fails to get into the chain, it will change to &quot;not accepted&quot; and not be spendable.  This may occasionally happen if another node generates a block within a few seconds of yours.</source>
        <translation>Las monedas generadas deben esperar 120 bloques antes de ser gastadas. Cuando has generado este bloque se emitió a la red para ser agregado en la cadena de bloques. Si falla al incluirse en la cadena, cambiará a &quot;no aceptado&quot; y las monedas no se podrán gastar. Esto puede ocurrir ocasionalmente si otro nodo genera un bloque casi al mismo tiempo que el tuyo.</translation>
    </message>
</context>
<context>
    <name>TransactionDescDialog</name>
    <message>
        <location filename="../forms/transactiondescdialog.ui" line="14"/>
        <source>Transaction details</source>
        <translation>Detalles de transacción</translation>
    </message>
    <message>
        <location filename="../forms/transactiondescdialog.ui" line="20"/>
        <source>This pane shows a detailed description of the transaction</source>
        <translation>Esta ventana muestra información detallada sobre la transacción</translation>
    </message>
</context>
<context>
    <name>TransactionTableModel</name>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Date</source>
        <translation>Fecha</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Type</source>
        <translation>Tipo</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Address</source>
        <translation>Dirección</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Amount</source>
        <translation>Cantidad</translation>
    </message>
    <message numerus="yes">
        <location filename="../transactiontablemodel.cpp" line="281"/>
        <source>Open for %n block(s)</source>
        <translation><numerusform>Abierto por %n bloque</numerusform><numerusform>Abierto por %n bloques</numerusform></translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="284"/>
        <source>Open until %1</source>
        <translation>Abierto hasta %1</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="287"/>
        <source>Offline (%1 confirmations)</source>
        <translation>Fuera de linea (%1 confirmaciones)</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="290"/>
        <source>Unconfirmed (%1 of %2 confirmations)</source>
        <translation>No confirmado (%1 de %2 confirmaciones)</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="293"/>
        <source>Confirmed (%1 confirmations)</source>
        <translation>Confirmado (%1 confirmaciones)</translation>
    </message>
    <message numerus="yes">
        <location filename="../transactiontablemodel.cpp" line="301"/>
        <source>Mined balance will be available in %n more blocks</source>
        <translation><numerusform>El balance minado estará disponible en %n bloque más</numerusform><numerusform>El balance minado estará disponible en %n bloques más</numerusform></translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="307"/>
        <source>This block was not received by any other nodes and will probably not be accepted!</source>
        <translation>Este bloque no ha sido recibido por otros nodos y probablemente no sea aceptado !</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="310"/>
        <source>Generated but not accepted</source>
        <translation>Generado pero no aceptado</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="353"/>
        <source>Received with</source>
        <translation>Recibido con</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="355"/>
        <source>Received from</source>
        <translation>Recibidos de</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="358"/>
        <source>Sent to</source>
        <translation>Enviado a</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="360"/>
        <source>Payment to yourself</source>
        <translation>Pago propio</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="362"/>
        <source>Mined</source>
        <translation>Minado</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="400"/>
        <source>(n/a)</source>
        <translation>(n/a)</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="599"/>
        <source>Transaction status. Hover over this field to show number of confirmations.</source>
        <translation>Estado de transacción. Pasa el ratón sobre este campo para ver el número de confirmaciones.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="601"/>
        <source>Date and time that the transaction was received.</source>
        <translation>Fecha y hora de cuando se recibió la transacción.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="603"/>
        <source>Type of transaction.</source>
        <translation>Tipo de transacción.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="605"/>
        <source>Destination address of transaction.</source>
        <translation>Dirección de destino de la transacción.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="607"/>
        <source>Amount removed from or added to balance.</source>
        <translation>Cantidad retirada o añadida al balance.</translation>
    </message>
</context>
<context>
    <name>TransactionView</name>
    <message>
        <location filename="../transactionview.cpp" line="55"/>
        <location filename="../transactionview.cpp" line="71"/>
        <source>All</source>
        <translation>Todo</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="56"/>
        <source>Today</source>
        <translation>Hoy</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="57"/>
        <source>This week</source>
        <translation>Esta semana</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="58"/>
        <source>This month</source>
        <translation>Este mes</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="59"/>
        <source>Last month</source>
        <translation>Mes pasado</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="60"/>
        <source>This year</source>
        <translation>Este año</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="61"/>
        <source>Range...</source>
        <translation>Rango...</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="72"/>
        <source>Received with</source>
        <translation>Recibido con</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="74"/>
        <source>Sent to</source>
        <translation>Enviado a</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="76"/>
        <source>To yourself</source>
        <translation>A ti mismo</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="77"/>
        <source>Mined</source>
        <translation>Minado</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="78"/>
        <source>Other</source>
        <translation>Otra</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="85"/>
        <source>Enter address or label to search</source>
        <translation>Introduzca una dirección o etiqueta que buscar</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="92"/>
        <source>Min amount</source>
        <translation>Cantidad mínima</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="126"/>
        <source>Copy address</source>
        <translation>Copiar dirección</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="127"/>
        <source>Copy label</source>
        <translation>Copiar etiqueta</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="128"/>
        <source>Copy amount</source>
        <translation>Copiar cuantía</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="129"/>
        <source>Edit label</source>
        <translation>Editar etiqueta</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="130"/>
        <source>Show transaction details</source>
        <translation>Mostrar detalles de la transacción</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="270"/>
        <source>Export Transaction Data</source>
        <translation>Exportar datos de la transacción</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="271"/>
        <source>Comma separated file (*.csv)</source>
        <translation>Archivos de columnas separadas por coma (*.csv)</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="279"/>
        <source>Confirmed</source>
        <translation>Confirmado</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="280"/>
        <source>Date</source>
        <translation>Fecha</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="281"/>
        <source>Type</source>
        <translation>Tipo</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="282"/>
        <source>Label</source>
        <translation>Etiqueta</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="283"/>
        <source>Address</source>
        <translation>Dirección</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="284"/>
        <source>Amount</source>
        <translation>Cantidad</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="285"/>
        <source>ID</source>
        <translation>ID</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="289"/>
        <source>Error exporting</source>
        <translation>Error exportando</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="289"/>
        <source>Could not write to file %1.</source>
        <translation>No se pudo escribir en el archivo %1.</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="384"/>
        <source>Range:</source>
        <translation>Rango:</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="392"/>
        <source>to</source>
        <translation>para</translation>
    </message>
</context>
<context>
    <name>VerifyMessageDialog</name>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="14"/>
        <source>Verify Signed Message</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="20"/>
        <source>Enter the message and signature below (be careful to correctly copy newlines, spaces, tabs and other invisible characters) to obtain the Bitcoin address used to sign the message.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="62"/>
        <source>Verify a message and obtain the Bitcoin address used to sign the message</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="65"/>
        <source>&amp;Verify Message</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="79"/>
        <source>Copy the currently selected address to the system clipboard</source>
        <translation>Copia la dirección seleccionada al portapapeles</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="82"/>
        <source>&amp;Copy Address</source>
        <translation>&amp;Copiar dirección</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="93"/>
        <source>Reset all verify message fields</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="96"/>
        <source>Clear &amp;All</source>
        <translation>Limpiar &amp;todo</translation>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="28"/>
        <source>Enter Bitcoin signature</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="29"/>
        <source>Click &quot;Verify Message&quot; to obtain address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="55"/>
        <location filename="../verifymessagedialog.cpp" line="62"/>
        <source>Invalid Signature</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="55"/>
        <source>The signature could not be decoded. Please check the signature and try again.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="62"/>
        <source>The signature did not match the message digest. Please check the signature and try again.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="72"/>
        <source>Address not found in address book.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="72"/>
        <source>Address found in address book: %1</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>WalletModel</name>
    <message>
        <location filename="../walletmodel.cpp" line="158"/>
        <source>Sending...</source>
        <translation>Enviando...</translation>
    </message>
</context>
<context>
    <name>WindowOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="313"/>
        <source>Window</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="316"/>
        <source>&amp;Minimize to the tray instead of the taskbar</source>
        <translation>&amp;Minimiza a la bandeja en vez de la barra de tareas</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="317"/>
        <source>Show only a tray icon after minimizing the window</source>
        <translation>Muestra solo el icono de sistema cuando se minimice la ventana</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="320"/>
        <source>M&amp;inimize on close</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="321"/>
        <source>Minimize instead of exit the application when the window is closed. When this option is enabled, the application will be closed only after selecting Quit in the menu.</source>
        <translation>Minimiza la ventana en lugar de salir de la aplicación.Cuando esta opcion está activa la aplicación solo se puede cerrar seleccionando Salir desde el menú.</translation>
    </message>
</context>
<context>
    <name>bitcoin-core</name>
    <message>
        <location filename="../bitcoinstrings.cpp" line="43"/>
        <source>Bitcoin version</source>
        <translation>Versión de Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="44"/>
        <source>Usage:</source>
        <translation>Uso:</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="45"/>
        <source>Send command to -server or bitcoind</source>
        <translation>Envíar comando a -server o bitcoind</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="46"/>
        <source>List commands</source>
        <translation>Muestra comandos
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="47"/>
        <source>Get help for a command</source>
        <translation>Recibir ayuda para un comando
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="49"/>
        <source>Options:</source>
        <translation>Opciones:
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="50"/>
        <source>Specify configuration file (default: bitcoin.conf)</source>
        <translation>Especifica archivo de configuración (predeterminado: bitcoin.conf)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="51"/>
        <source>Specify pid file (default: bitcoind.pid)</source>
        <translation>Especifica archivo pid (predeterminado: bitcoin.pid)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="52"/>
        <source>Generate coins</source>
        <translation>Generar monedas</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="53"/>
        <source>Don&apos;t generate coins</source>
        <translation>No generar monedas</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="54"/>
        <source>Specify data directory</source>
        <translation>Especificar directorio para los datos</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="55"/>
        <source>Set database cache size in megabytes (default: 25)</source>
        <translation>Establecer el tamaño del caché de la base de datos en megabytes (por defecto: 25)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="56"/>
        <source>Set database disk log size in megabytes (default: 100)</source>
        <translation>Base de datos de conjunto de discos de registro de tamaño en megabytes (por defecto: 100)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="57"/>
        <source>Specify connection timeout (in milliseconds)</source>
        <translation>Especifica tiempo de espera para conexion (en milisegundos)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="63"/>
        <source>Listen for connections on &lt;port&gt; (default: 8333 or testnet: 18333)</source>
        <translation>Preste atención a las conexiones en &lt;puerto&gt; (por defecto: 8333 o testnet: 18333)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="64"/>
        <source>Maintain at most &lt;n&gt; connections to peers (default: 125)</source>
        <translation>Mantener en la mayoría de las conexiones &lt;n&gt; a sus compañeros (por defecto: 125)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="66"/>
        <source>Connect only to the specified node</source>
        <translation>Conecta solo al nodo especificado
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="67"/>
        <source>Connect to a node to retrieve peer addresses, and disconnect</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="68"/>
        <source>Specify your own public address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="69"/>
        <source>Only connect to nodes in network &lt;net&gt; (IPv4 or IPv6)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="70"/>
        <source>Try to discover public IP address (default: 1)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="73"/>
        <source>Bind to given address. Use [host]:port notation for IPv6</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="75"/>
        <source>Threshold for disconnecting misbehaving peers (default: 100)</source>
        <translation>Umbral para la desconexión de los compañeros se portan mal (por defecto: 100)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="76"/>
        <source>Number of seconds to keep misbehaving peers from reconnecting (default: 86400)</source>
        <translation>Número de segundos que se mantienen los compañeros se portan mal en volver a conectarse (por defecto: 86400)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="79"/>
        <source>Maximum per-connection receive buffer, &lt;n&gt;*1000 bytes (default: 10000)</source>
        <translation>Máximo por-conexión búfer de recepción, &lt;n&gt;*1000 bytes (por defecto: 10000)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="80"/>
        <source>Maximum per-connection send buffer, &lt;n&gt;*1000 bytes (default: 10000)</source>
        <translation>Máximo por conexión buffer de envío, &lt;n&gt;*1000 bytes (por defecto: 10000)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="83"/>
        <source>Detach block and address databases. Increases shutdown time (default: 0)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="86"/>
        <source>Accept command line and JSON-RPC commands</source>
        <translation>Aceptar comandos consola y JSON-RPC
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="87"/>
        <source>Run in the background as a daemon and accept commands</source>
        <translation>Correr como demonio y acepta comandos
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="88"/>
        <source>Use the test network</source>
        <translation>Usa la red de pruebas
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="89"/>
        <source>Output extra debugging information</source>
        <translation>Salida de información de depuración extra</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="90"/>
        <source>Prepend debug output with timestamp</source>
        <translation>Anteponer la salida de depuración, con indicación de la hora</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="91"/>
        <source>Send trace/debug info to console instead of debug.log file</source>
        <translation>Enviar rastrear/debug info a la consola en lugar de debug.log archivo</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="92"/>
        <source>Send trace/debug info to debugger</source>
        <translation>Enviar rastrear / debug info al depurador</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="93"/>
        <source>Username for JSON-RPC connections</source>
        <translation>Usuario para las conexiones JSON-RPC
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="94"/>
        <source>Password for JSON-RPC connections</source>
        <translation>Contraseña para las conexiones JSON-RPC
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="95"/>
        <source>Listen for JSON-RPC connections on &lt;port&gt; (default: 8332)</source>
        <translation>Escucha conexiones JSON-RPC en el puerto &lt;port&gt; (predeterminado: 8332)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="96"/>
        <source>Allow JSON-RPC connections from specified IP address</source>
        <translation>Permite conexiones JSON-RPC desde la dirección IP especificada
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="97"/>
        <source>Send commands to node running on &lt;ip&gt; (default: 127.0.0.1)</source>
        <translation>Envía comando al nodo situado en &lt;ip&gt; (predeterminado: 127.0.0.1)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="98"/>
        <source>Execute command when the best block changes (%s in cmd is replaced by block hash)</source>
        <translation>Ejecutar un comando cuando cambia el mejor bloque (%s en cmd se sustituye por el hash de bloque)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="101"/>
        <source>Upgrade wallet to latest format</source>
        <translation>Actualizar el monedero al último formato</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="102"/>
        <source>Set key pool size to &lt;n&gt; (default: 100)</source>
        <translation>Ajusta el número de claves en reserva &lt;n&gt; (predeterminado: 100)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="103"/>
        <source>Rescan the block chain for missing wallet transactions</source>
        <translation>Volver a examinar la cadena de bloques en busca de transacciones del monedero perdidas</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="104"/>
        <source>How many blocks to check at startup (default: 2500, 0 = all)</source>
        <translation>Cuántos bloques para comprobar en el arranque (por defecto: 2500, 0 = todos)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="105"/>
        <source>How thorough the block verification is (0-6, default: 1)</source>
        <translation>Cómo completa la verificación del bloque es (0-6, por defecto: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="106"/>
        <source>Imports blocks from external blk000?.dat file</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="108"/>
        <source>
SSL options: (see the Bitcoin Wiki for SSL setup instructions)</source>
        <translation>Opciones SSL: (ver la Bitcoin Wiki para instrucciones de configuración SSL)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="111"/>
        <source>Use OpenSSL (https) for JSON-RPC connections</source>
        <translation>Usa OpenSSL (https) para las conexiones JSON-RPC
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="112"/>
        <source>Server certificate file (default: server.cert)</source>
        <translation>Certificado del servidor (Predeterminado: server.cert)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="113"/>
        <source>Server private key (default: server.pem)</source>
        <translation>Clave privada del servidor (Predeterminado: server.pem)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="114"/>
        <source>Acceptable ciphers (default: TLSv1+HIGH:!SSLv2:!aNULL:!eNULL:!AH:!3DES:@STRENGTH)</source>
        <translation>Cifrados aceptados (Predeterminado: TLSv1+HIGH:!SSLv2:!aNULL:!eNULL:!AH:!3DES:@STRENGTH)
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="145"/>
        <source>Warning: Disk space is low</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="107"/>
        <source>This help message</source>
        <translation>Este mensaje de ayuda
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="121"/>
        <source>Cannot obtain a lock on data directory %s.  Bitcoin is probably already running.</source>
        <translation>No se puede obtener permiso de trabajo en la carpeta de datos %s. Probablemente Bitcoin ya se está ejecutando.
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="48"/>
        <source>Bitcoin</source>
        <translation>Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="30"/>
        <source>Unable to bind to %s on this computer (bind returned error %d, %s)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="58"/>
        <source>Connect through socks proxy</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="59"/>
        <source>Select the version of socks proxy to use (4 or 5, 5 is default)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="60"/>
        <source>Do not use proxy for connections to network &lt;net&gt; (IPv4 or IPv6)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="61"/>
        <source>Allow DNS lookups for -addnode, -seednode and -connect</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="62"/>
        <source>Pass DNS requests to (SOCKS5) proxy</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="142"/>
        <source>Loading addresses...</source>
        <translation>Cargando direcciones...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="132"/>
        <source>Error loading blkindex.dat</source>
        <translation>Error al cargar blkindex.dat</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="134"/>
        <source>Error loading wallet.dat: Wallet corrupted</source>
        <translation>Error al cargar wallet.dat: el monedero está dañado</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="135"/>
        <source>Error loading wallet.dat: Wallet requires newer version of Bitcoin</source>
        <translation>Error al cargar wallet.dat: El monedero requiere una versión más reciente de Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="136"/>
        <source>Wallet needed to be rewritten: restart Bitcoin to complete</source>
        <translation>El monedero ha necesitado ser reescrito. Reinicie Bitcoin para completar el proceso</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="137"/>
        <source>Error loading wallet.dat</source>
        <translation>Error al cargar wallet.dat</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="124"/>
        <source>Invalid -proxy address: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="125"/>
        <source>Unknown network specified in -noproxy: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="127"/>
        <source>Unknown network specified in -onlynet: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="126"/>
        <source>Unknown -socks proxy version requested: %i</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="128"/>
        <source>Cannot resolve -bind address: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="129"/>
        <source>Not listening on any port</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="130"/>
        <source>Cannot resolve -externalip address: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="117"/>
        <source>Invalid amount for -paytxfee=&lt;amount&gt;: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="143"/>
        <source>Error: could not start node</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="31"/>
        <source>Error: Wallet locked, unable to create transaction  </source>
        <translation>Error: monedero bloqueado. Bitcoin es incapaz de crear las transacciones  </translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="32"/>
        <source>Error: This transaction requires a transaction fee of at least %s because of its amount, complexity, or use of recently received funds  </source>
        <translation>Error: esta transacción está sujeta a una tarifa de %s, bien por su cantidad, complejidad, o por el uso de fondos recientemente recibidos  </translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="35"/>
        <source>Error: Transaction creation failed  </source>
        <translation>Error: no se ha podido crear la transacción</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="36"/>
        <source>Sending...</source>
        <translation>Enviando...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="37"/>
        <source>Error: The transaction was rejected.  This might happen if some of the coins in your wallet were already spent, such as if you used a copy of wallet.dat and coins were spent in the copy but not marked as spent here.</source>
        <translation>Error: la transacción fue rechazada. Esto puede pasar si alguna de las monedas ya estaba gastada o si ha usado una copia de wallet.dat y las monedas se gastaron en la copia pero no se han marcado como gastadas aquí.</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="41"/>
        <source>Invalid amount</source>
        <translation>Cuantía no válida</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="42"/>
        <source>Insufficient funds</source>
        <translation>Fondos insuficientes</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="131"/>
        <source>Loading block index...</source>
        <translation>Cargando el índice de bloques...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="65"/>
        <source>Add a node to connect to and attempt to keep the connection open</source>
        <translation>Añadir un nodo para conectarse y tratar de mantener la conexión abierta</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="28"/>
        <source>Unable to bind to %s on this computer. Bitcoin is probably already running.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="71"/>
        <source>Find peers using internet relay chat (default: 0)</source>
        <translation>Encontrar los pares utilizando Internet Relay Chat (por defecto: 0)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="72"/>
        <source>Accept connections from outside (default: 1)</source>
        <translation>Aceptar conexiones desde el exterior (por defecto: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="74"/>
        <source>Find peers using DNS lookup (default: 1)</source>
        <translation>Encontrar compañeros con búsqueda de DNS (por defecto: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="81"/>
        <source>Use Universal Plug and Play to map the listening port (default: 1)</source>
        <translation>Use Universal Plug and Play para asignar el puerto de escucha (por defecto: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="82"/>
        <source>Use Universal Plug and Play to map the listening port (default: 0)</source>
        <translation>Use Universal Plug and Play para asignar el puerto de escucha (por defecto: 0)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="85"/>
        <source>Fee per KB to add to transactions you send</source>
        <translation>Tarifa por KB que añadir a las transacciones que envíe</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="118"/>
        <source>Warning: -paytxfee is set very high. This is the transaction fee you will pay if you send a transaction.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="133"/>
        <source>Loading wallet...</source>
        <translation>Cargando monedero...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="138"/>
        <source>Cannot downgrade wallet</source>
        <translation>No se puede rebajar el monedero</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="139"/>
        <source>Cannot initialize keypool</source>
        <translation>No se puede inicializar grupo de teclas</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="140"/>
        <source>Cannot write default address</source>
        <translation>No se puede escribir la dirección por defecto</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="141"/>
        <source>Rescanning...</source>
        <translation>Rescaneando...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="144"/>
        <source>Done loading</source>
        <translation>Generado pero no aceptado</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="8"/>
        <source>To use the %s option</source>
        <translation>Para utilizar la opción %s</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="9"/>
        <source>%s, you must set a rpcpassword in the configuration file:
 %s
It is recommended you use the following random password:
rpcuser=bitcoinrpc
rpcpassword=%s
(you do not need to remember this password)
If the file does not exist, create it with owner-readable-only file permissions.
</source>
        <translation>%s, tiene que establecer rpcpassword en el archivo de configuración: ⏎
%s ⏎
Se recomienda utilizar la siguiente contraseña aleatoria: ⏎
rpcuser = bitcoinrpc ⏎
rpcpassword =%s ⏎
(no es necesario para recordar esta contraseña) ⏎
Si el archivo no existe se crea con los permisos de lectura y escritura solamente del propietario. ⏎</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="18"/>
        <source>Error</source>
        <translation>Error</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="19"/>
        <source>An error occured while setting up the RPC port %i for listening: %s</source>
        <translation>Ha ocurrido un error al instalar el puerto RPC %i para escuchar: %s</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="20"/>
        <source>You must set rpcpassword=&lt;password&gt; in the configuration file:
%s
If the file does not exist, create it with owner-readable-only file permissions.</source>
        <translation>Tiene que establecer rpcpassword=&lt;contraseña&gt; en el fichero de configuración: ⏎
%s ⏎
Si el archivo no existe, se crea con permisos de propietario de lectura de sólo archivos.</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="25"/>
        <source>Warning: Please check that your computer&apos;s date and time are correct.  If your clock is wrong Bitcoin will not work properly.</source>
        <translation>Precaución: revise por favor que la fecha y hora de su sistema son correctas. Si el reloj está mal Bitcoin no funcionará correctamente.</translation>
    </message>
</context>
</TS>